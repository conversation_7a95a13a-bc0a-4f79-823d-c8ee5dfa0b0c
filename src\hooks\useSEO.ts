import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { seoOptimizer, type SEOMetadata } from '@/services/seo-optimizer';

interface UseSEOOptions {
  title?: string;
  description?: string;
  keywords?: string[];
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'profile';
  noIndex?: boolean;
  noFollow?: boolean;
}

export const useSEO = (options: UseSEOOptions = {}) => {
  const location = useLocation();
  const currentUrl = window.location.origin + location.pathname;

  useEffect(() => {
    // Set document title
    if (options.title) {
      document.title = options.title;
    }

    // Update meta tags
    updateMetaTags({
      title: options.title,
      description: options.description,
      keywords: options.keywords,
      image: options.image,
      url: options.url || currentUrl,
      type: options.type || 'website',
      noIndex: options.noIndex,
      noFollow: options.noFollow
    });

    // Update Open Graph tags
    updateOpenGraphTags({
      title: options.title,
      description: options.description,
      image: options.image,
      url: options.url || currentUrl,
      type: options.type || 'website'
    });

    // Update Twitter Card tags
    updateTwitterCardTags({
      title: options.title,
      description: options.description,
      image: options.image
    });

    // Update canonical URL
    updateCanonicalUrl(options.url || currentUrl);

  }, [options.title, options.description, options.keywords, options.image, options.url, options.type, options.noIndex, options.noFollow, currentUrl]);
};

export const useSEOFromMetadata = (metadata: SEOMetadata | null) => {
  const location = useLocation();

  useEffect(() => {
    if (!metadata) return;

    // Set document title
    document.title = metadata.title;

    // Update all meta tags from metadata
    updateMetaTags({
      title: metadata.title,
      description: metadata.description,
      keywords: metadata.keywords,
      image: metadata.ogImage,
      url: metadata.canonicalUrl,
      type: 'website'
    });

    updateOpenGraphTags({
      title: metadata.ogTitle || metadata.title,
      description: metadata.ogDescription || metadata.description,
      image: metadata.ogImage,
      url: metadata.ogUrl,
      type: 'website'
    });

    updateTwitterCardTags({
      title: metadata.twitterTitle || metadata.title,
      description: metadata.twitterDescription || metadata.description,
      image: metadata.twitterImage || metadata.ogImage,
      card: metadata.twitterCard
    });

    updateCanonicalUrl(metadata.canonicalUrl);

    // Add structured data
    if (metadata.structuredData) {
      updateStructuredData(metadata.structuredData);
    }

  }, [metadata, location.pathname]);
};

// Helper functions to update meta tags
const updateMetaTags = (options: {
  title?: string;
  description?: string;
  keywords?: string[];
  image?: string;
  url?: string;
  type?: string;
  noIndex?: boolean;
  noFollow?: boolean;
}) => {
  // Description
  if (options.description) {
    updateMetaTag('name', 'description', options.description);
  }

  // Keywords
  if (options.keywords && options.keywords.length > 0) {
    updateMetaTag('name', 'keywords', options.keywords.join(', '));
  }

  // Robots
  const robotsContent = [];
  if (options.noIndex) robotsContent.push('noindex');
  if (options.noFollow) robotsContent.push('nofollow');
  if (robotsContent.length === 0) robotsContent.push('index', 'follow');
  
  updateMetaTag('name', 'robots', robotsContent.join(', '));

  // Author
  updateMetaTag('name', 'author', 'Developer Portfolios');

  // Viewport (ensure it's set)
  updateMetaTag('name', 'viewport', 'width=device-width, initial-scale=1.0');
};

const updateOpenGraphTags = (options: {
  title?: string;
  description?: string;
  image?: string;
  url?: string;
  type?: string;
}) => {
  if (options.title) {
    updateMetaTag('property', 'og:title', options.title);
  }

  if (options.description) {
    updateMetaTag('property', 'og:description', options.description);
  }

  if (options.image) {
    updateMetaTag('property', 'og:image', options.image);
    updateMetaTag('property', 'og:image:alt', options.title || 'Developer Portfolio');
  }

  if (options.url) {
    updateMetaTag('property', 'og:url', options.url);
  }

  updateMetaTag('property', 'og:type', options.type || 'website');
  updateMetaTag('property', 'og:site_name', 'Developer Portfolios');
};

const updateTwitterCardTags = (options: {
  title?: string;
  description?: string;
  image?: string;
  card?: 'summary' | 'summary_large_image';
}) => {
  updateMetaTag('name', 'twitter:card', options.card || 'summary_large_image');

  if (options.title) {
    updateMetaTag('name', 'twitter:title', options.title);
  }

  if (options.description) {
    updateMetaTag('name', 'twitter:description', options.description);
  }

  if (options.image) {
    updateMetaTag('name', 'twitter:image', options.image);
  }

  // Add Twitter site handle if available
  updateMetaTag('name', 'twitter:site', '@devportfolios');
};

const updateCanonicalUrl = (url?: string) => {
  if (!url) return;

  let canonical = document.querySelector('link[rel="canonical"]') as HTMLLinkElement;
  
  if (!canonical) {
    canonical = document.createElement('link');
    canonical.rel = 'canonical';
    document.head.appendChild(canonical);
  }
  
  canonical.href = url;
};

const updateStructuredData = (data: Record<string, any>) => {
  // Remove existing structured data
  const existingScript = document.querySelector('script[type="application/ld+json"]');
  if (existingScript) {
    existingScript.remove();
  }

  // Add new structured data
  const script = document.createElement('script');
  script.type = 'application/ld+json';
  script.textContent = JSON.stringify(data);
  document.head.appendChild(script);
};

const updateMetaTag = (attribute: 'name' | 'property', value: string, content: string) => {
  let tag = document.querySelector(`meta[${attribute}="${value}"]`) as HTMLMetaElement;
  
  if (!tag) {
    tag = document.createElement('meta');
    tag.setAttribute(attribute, value);
    document.head.appendChild(tag);
  }
  
  tag.content = content;
};

// Hook for generating and caching SEO data
export const useGenerateSEO = () => {
  const generateUserProfileSEO = (user: any, baseUrl?: string) => {
    const metadata = seoOptimizer.generateUserProfileSEO(user, baseUrl || window.location.origin);
    const url = `${window.location.origin}/profile/${user.username || user.uid}`;
    
    // Cache the SEO data
    seoOptimizer.cacheSEOData(url, metadata);
    
    return metadata;
  };

  const generatePortfoliosSEO = (users: any[], baseUrl?: string) => {
    const metadata = seoOptimizer.generatePortfoliosSEO(users, baseUrl || window.location.origin);
    const url = `${window.location.origin}/portfolios`;
    
    // Cache the SEO data
    seoOptimizer.cacheSEOData(url, metadata);
    
    return metadata;
  };

  const generateHomeSEO = (featuredUsers: any[], baseUrl?: string) => {
    const metadata = seoOptimizer.generateHomeSEO(featuredUsers, baseUrl || window.location.origin);
    const url = window.location.origin;
    
    // Cache the SEO data
    seoOptimizer.cacheSEOData(url, metadata);
    
    return metadata;
  };

  const getCachedSEO = (url?: string) => {
    return seoOptimizer.getCachedSEOData(url || window.location.href);
  };

  return {
    generateUserProfileSEO,
    generatePortfoliosSEO,
    generateHomeSEO,
    getCachedSEO
  };
};

// Hook for preloading SEO data
export const usePreloadSEO = () => {
  const preloadUserSEO = async (username: string) => {
    const url = `${window.location.origin}/profile/${username}`;
    const cached = seoOptimizer.getCachedSEOData(url);
    
    if (!cached) {
      // This would typically fetch user data and generate SEO
      console.log(`Preloading SEO for user: ${username}`);
    }
  };

  const preloadPageSEO = async (path: string) => {
    const url = `${window.location.origin}${path}`;
    const cached = seoOptimizer.getCachedSEOData(url);
    
    if (!cached) {
      console.log(`Preloading SEO for page: ${path}`);
    }
  };

  return {
    preloadUserSEO,
    preloadPageSEO
  };
};

export default useSEO;
