interface CacheEvent {
  type: 'hit' | 'miss' | 'set' | 'delete' | 'clear' | 'preload';
  key: string;
  timestamp: number;
  duration?: number;
  size?: number;
  source?: 'memory' | 'localStorage' | 'firebase';
  metadata?: Record<string, any>;
}

interface CacheMetrics {
  totalRequests: number;
  hits: number;
  misses: number;
  hitRate: number;
  averageResponseTime: number;
  totalDataTransferred: number;
  cacheSize: number;
  topKeys: Array<{ key: string; hits: number; misses: number; hitRate: number }>;
  timeSeriesData: Array<{ timestamp: number; hits: number; misses: number; hitRate: number }>;
  performanceByKey: Map<string, {
    hits: number;
    misses: number;
    totalTime: number;
    averageTime: number;
    lastAccessed: number;
  }>;
}

interface CacheAlert {
  id: string;
  type: 'warning' | 'error' | 'info';
  message: string;
  timestamp: number;
  resolved: boolean;
  metadata?: Record<string, any>;
}

class CacheAnalytics {
  private events: CacheEvent[] = [];
  private alerts: CacheAlert[] = [];
  private maxEvents = 1000; // Keep last 1000 events
  private maxAlerts = 50; // Keep last 50 alerts
  private performanceThresholds = {
    lowHitRate: 0.5, // Alert if hit rate below 50%
    slowResponse: 1000, // Alert if response time > 1s
    highMemoryUsage: 0.8 // Alert if cache uses > 80% of max size
  };

  // Track cache events
  trackEvent(event: Omit<CacheEvent, 'timestamp'>): void {
    const fullEvent: CacheEvent = {
      ...event,
      timestamp: Date.now()
    };

    this.events.push(fullEvent);

    // Keep only recent events
    if (this.events.length > this.maxEvents) {
      this.events = this.events.slice(-this.maxEvents);
    }

    // Check for alerts
    this.checkAlerts(fullEvent);

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`[Cache Analytics] ${event.type.toUpperCase()}: ${event.key}`, fullEvent);
    }
  }

  // Generate comprehensive metrics
  getMetrics(timeWindow?: number): CacheMetrics {
    const now = Date.now();
    const windowStart = timeWindow ? now - timeWindow : 0;
    
    const relevantEvents = this.events.filter(e => e.timestamp >= windowStart);
    
    const hits = relevantEvents.filter(e => e.type === 'hit').length;
    const misses = relevantEvents.filter(e => e.type === 'miss').length;
    const totalRequests = hits + misses;
    const hitRate = totalRequests > 0 ? (hits / totalRequests) * 100 : 0;

    // Calculate average response time
    const timedEvents = relevantEvents.filter(e => e.duration !== undefined);
    const averageResponseTime = timedEvents.length > 0 
      ? timedEvents.reduce((sum, e) => sum + (e.duration || 0), 0) / timedEvents.length
      : 0;

    // Calculate total data transferred
    const totalDataTransferred = relevantEvents
      .filter(e => e.size !== undefined)
      .reduce((sum, e) => sum + (e.size || 0), 0);

    // Get current cache size
    const cacheSize = this.getCurrentCacheSize();

    // Generate top keys performance
    const keyPerformance = this.getKeyPerformance(relevantEvents);
    const topKeys = Array.from(keyPerformance.entries())
      .map(([key, stats]) => ({
        key,
        hits: stats.hits,
        misses: stats.misses,
        hitRate: stats.hits + stats.misses > 0 ? (stats.hits / (stats.hits + stats.misses)) * 100 : 0
      }))
      .sort((a, b) => (b.hits + b.misses) - (a.hits + a.misses))
      .slice(0, 10);

    // Generate time series data (last 24 hours in 1-hour buckets)
    const timeSeriesData = this.generateTimeSeriesData(relevantEvents);

    return {
      totalRequests,
      hits,
      misses,
      hitRate,
      averageResponseTime,
      totalDataTransferred,
      cacheSize,
      topKeys,
      timeSeriesData,
      performanceByKey: keyPerformance
    };
  }

  private getKeyPerformance(events: CacheEvent[]): Map<string, {
    hits: number;
    misses: number;
    totalTime: number;
    averageTime: number;
    lastAccessed: number;
  }> {
    const keyStats = new Map();

    events.forEach(event => {
      if (!keyStats.has(event.key)) {
        keyStats.set(event.key, {
          hits: 0,
          misses: 0,
          totalTime: 0,
          averageTime: 0,
          lastAccessed: 0
        });
      }

      const stats = keyStats.get(event.key);
      
      if (event.type === 'hit') stats.hits++;
      if (event.type === 'miss') stats.misses++;
      if (event.duration) stats.totalTime += event.duration;
      stats.lastAccessed = Math.max(stats.lastAccessed, event.timestamp);
    });

    // Calculate average times
    keyStats.forEach((stats, key) => {
      const totalRequests = stats.hits + stats.misses;
      stats.averageTime = totalRequests > 0 ? stats.totalTime / totalRequests : 0;
    });

    return keyStats;
  }

  private generateTimeSeriesData(events: CacheEvent[]): Array<{ 
    timestamp: number; 
    hits: number; 
    misses: number; 
    hitRate: number; 
  }> {
    const now = Date.now();
    const hourMs = 60 * 60 * 1000;
    const buckets = new Map();

    // Initialize 24 hour buckets
    for (let i = 23; i >= 0; i--) {
      const bucketTime = now - (i * hourMs);
      const bucketKey = Math.floor(bucketTime / hourMs) * hourMs;
      buckets.set(bucketKey, { hits: 0, misses: 0 });
    }

    // Fill buckets with events
    events.forEach(event => {
      if (event.type === 'hit' || event.type === 'miss') {
        const bucketKey = Math.floor(event.timestamp / hourMs) * hourMs;
        if (buckets.has(bucketKey)) {
          const bucket = buckets.get(bucketKey);
          if (event.type === 'hit') bucket.hits++;
          if (event.type === 'miss') bucket.misses++;
        }
      }
    });

    // Convert to array with hit rates
    return Array.from(buckets.entries()).map(([timestamp, data]) => ({
      timestamp,
      hits: data.hits,
      misses: data.misses,
      hitRate: data.hits + data.misses > 0 ? (data.hits / (data.hits + data.misses)) * 100 : 0
    }));
  }

  private getCurrentCacheSize(): number {
    try {
      let totalSize = 0;
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith('firebase_cache_')) {
          const value = localStorage.getItem(key);
          if (value) totalSize += value.length;
        }
      }
      return totalSize;
    } catch {
      return 0;
    }
  }

  private checkAlerts(event: CacheEvent): void {
    const metrics = this.getMetrics(60 * 60 * 1000); // Last hour

    // Low hit rate alert
    if (metrics.totalRequests > 10 && metrics.hitRate < this.performanceThresholds.lowHitRate * 100) {
      this.addAlert({
        type: 'warning',
        message: `Low cache hit rate: ${metrics.hitRate.toFixed(1)}%`,
        metadata: { hitRate: metrics.hitRate, threshold: this.performanceThresholds.lowHitRate * 100 }
      });
    }

    // Slow response alert
    if (event.duration && event.duration > this.performanceThresholds.slowResponse) {
      this.addAlert({
        type: 'warning',
        message: `Slow cache response for ${event.key}: ${event.duration}ms`,
        metadata: { key: event.key, duration: event.duration, threshold: this.performanceThresholds.slowResponse }
      });
    }

    // High memory usage alert
    const maxCacheSize = 5 * 1024 * 1024; // 5MB
    if (metrics.cacheSize > maxCacheSize * this.performanceThresholds.highMemoryUsage) {
      this.addAlert({
        type: 'warning',
        message: `High cache memory usage: ${(metrics.cacheSize / 1024 / 1024).toFixed(1)}MB`,
        metadata: { size: metrics.cacheSize, threshold: maxCacheSize * this.performanceThresholds.highMemoryUsage }
      });
    }
  }

  private addAlert(alert: Omit<CacheAlert, 'id' | 'timestamp' | 'resolved'>): void {
    const newAlert: CacheAlert = {
      ...alert,
      id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now(),
      resolved: false
    };

    this.alerts.push(newAlert);

    // Keep only recent alerts
    if (this.alerts.length > this.maxAlerts) {
      this.alerts = this.alerts.slice(-this.maxAlerts);
    }

    console.warn(`[Cache Alert] ${alert.message}`, newAlert);
  }

  // Get active alerts
  getAlerts(includeResolved = false): CacheAlert[] {
    return includeResolved 
      ? [...this.alerts]
      : this.alerts.filter(alert => !alert.resolved);
  }

  // Resolve an alert
  resolveAlert(alertId: string): void {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.resolved = true;
    }
  }

  // Clear all events and alerts
  clear(): void {
    this.events = [];
    this.alerts = [];
  }

  // Export analytics data
  exportData(): {
    events: CacheEvent[];
    alerts: CacheAlert[];
    metrics: CacheMetrics;
    exportTime: number;
  } {
    return {
      events: [...this.events],
      alerts: [...this.alerts],
      metrics: this.getMetrics(),
      exportTime: Date.now()
    };
  }

  // Get recent events
  getRecentEvents(limit = 50): CacheEvent[] {
    return this.events.slice(-limit);
  }
}

// Export singleton instance
export const cacheAnalytics = new CacheAnalytics();

// Export types
export type { CacheEvent, CacheMetrics, CacheAlert };
