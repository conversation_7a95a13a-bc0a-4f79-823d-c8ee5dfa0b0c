import type { UserProfile } from '@/services/firebase-db';

interface SEOMetadata {
  title: string;
  description: string;
  keywords: string[];
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  ogUrl?: string;
  twitterCard?: 'summary' | 'summary_large_image';
  twitterTitle?: string;
  twitterDescription?: string;
  twitterImage?: string;
  canonicalUrl?: string;
  structuredData?: Record<string, any>;
}

interface SEOCacheEntry {
  metadata: SEOMetadata;
  timestamp: number;
  url: string;
  userAgent?: string;
}

class SEOOptimizer {
  private seoCache = new Map<string, SEOCacheEntry>();
  private readonly CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours for SEO data
  private readonly MAX_CACHE_SIZE = 500;

  // Generate SEO metadata for user profiles
  generateUserProfileSEO(user: UserProfile, baseUrl: string = ''): SEOMetadata {
    const displayName = user.displayName || 'Developer';
    const username = user.username || user.uid;
    const bio = user.bio || '';
    const skills = user.skills || [];
    const profileUrl = `${baseUrl}/profile/${username}`;
    
    // Generate rich description
    const description = this.generateProfileDescription(user);
    
    // Generate keywords from user data
    const keywords = this.generateProfileKeywords(user);
    
    // Create structured data for rich snippets
    const structuredData = this.generatePersonStructuredData(user, profileUrl);

    return {
      title: `${displayName} - Developer Portfolio | ${user.title || 'Full Stack Developer'}`,
      description,
      keywords,
      ogTitle: `${displayName}'s Developer Portfolio`,
      ogDescription: description,
      ogImage: user.photoURL || `${baseUrl}/default-profile-og.jpg`,
      ogUrl: profileUrl,
      twitterCard: 'summary_large_image',
      twitterTitle: `${displayName} - Developer Portfolio`,
      twitterDescription: description.substring(0, 200),
      twitterImage: user.photoURL || `${baseUrl}/default-profile-twitter.jpg`,
      canonicalUrl: profileUrl,
      structuredData
    };
  }

  // Generate SEO metadata for portfolios page
  generatePortfoliosSEO(users: UserProfile[], baseUrl: string = ''): SEOMetadata {
    const totalUsers = users.length;
    const featuredCount = users.filter(u => u.isFeatured).length;
    const verifiedCount = users.filter(u => u.isVerified).length;
    
    const topSkills = this.getTopSkills(users).slice(0, 10);
    
    const description = `Discover ${totalUsers} talented developers and their portfolios. Browse ${featuredCount} featured developers and ${verifiedCount} verified professionals. Find experts in ${topSkills.join(', ')} and more.`;
    
    const keywords = [
      'developer portfolios',
      'software developers',
      'web developers',
      'programming portfolios',
      'tech talent',
      'hire developers',
      ...topSkills.map(skill => `${skill} developers`),
      'coding projects',
      'developer showcase'
    ];

    return {
      title: `Developer Portfolios - Browse ${totalUsers} Talented Developers`,
      description,
      keywords,
      ogTitle: 'Developer Portfolios - Find Amazing Talent',
      ogDescription: description,
      ogImage: `${baseUrl}/portfolios-og.jpg`,
      ogUrl: `${baseUrl}/portfolios`,
      twitterCard: 'summary_large_image',
      twitterTitle: 'Developer Portfolios - Amazing Talent',
      twitterDescription: description.substring(0, 200),
      canonicalUrl: `${baseUrl}/portfolios`,
      structuredData: this.generatePortfoliosStructuredData(users, baseUrl)
    };
  }

  // Generate SEO metadata for home page
  generateHomeSEO(featuredUsers: UserProfile[], baseUrl: string = ''): SEOMetadata {
    const featuredCount = featuredUsers.length;
    const topSkills = this.getTopSkills(featuredUsers).slice(0, 8);
    
    const description = `Discover amazing developer portfolios and connect with talented professionals. Browse ${featuredCount} featured developers specializing in ${topSkills.join(', ')} and more. Find your next team member or showcase your own work.`;
    
    const keywords = [
      'developer portfolios',
      'software developer showcase',
      'programming talent',
      'web developer portfolios',
      'tech professionals',
      'coding projects',
      'developer community',
      ...topSkills,
      'hire developers',
      'freelance developers'
    ];

    return {
      title: 'Developer Portfolios - Showcase Your Code, Find Amazing Talent',
      description,
      keywords,
      ogTitle: 'Developer Portfolios - Amazing Code, Amazing People',
      ogDescription: description,
      ogImage: `${baseUrl}/home-og.jpg`,
      ogUrl: baseUrl,
      twitterCard: 'summary_large_image',
      twitterTitle: 'Developer Portfolios - Amazing Talent',
      twitterDescription: description.substring(0, 200),
      canonicalUrl: baseUrl,
      structuredData: this.generateWebsiteStructuredData(baseUrl)
    };
  }

  private generateProfileDescription(user: UserProfile): string {
    const displayName = user.displayName || 'Developer';
    const title = user.title || 'Software Developer';
    const bio = user.bio || '';
    const skills = user.skills || [];
    const location = user.location || '';
    
    let description = `${displayName} is a ${title}`;
    
    if (location) {
      description += ` based in ${location}`;
    }
    
    if (skills.length > 0) {
      const topSkills = skills.slice(0, 5);
      description += ` specializing in ${topSkills.join(', ')}`;
    }
    
    if (bio) {
      description += `. ${bio.substring(0, 100)}`;
    }
    
    description += '. View portfolio, projects, and contact information.';
    
    return description.substring(0, 160); // Keep under 160 chars for meta description
  }

  private generateProfileKeywords(user: UserProfile): string[] {
    const keywords = new Set<string>();
    
    // Add basic keywords
    keywords.add('developer portfolio');
    keywords.add('software developer');
    keywords.add('programmer');
    
    // Add name-based keywords
    if (user.displayName) {
      keywords.add(user.displayName.toLowerCase());
    }
    
    // Add title-based keywords
    if (user.title) {
      keywords.add(user.title.toLowerCase());
      keywords.add(`${user.title.toLowerCase()} portfolio`);
    }
    
    // Add skill-based keywords
    if (user.skills) {
      user.skills.forEach(skill => {
        keywords.add(skill.toLowerCase());
        keywords.add(`${skill.toLowerCase()} developer`);
      });
    }
    
    // Add location-based keywords
    if (user.location) {
      keywords.add(`developer ${user.location.toLowerCase()}`);
    }
    
    return Array.from(keywords).slice(0, 20); // Limit to 20 keywords
  }

  private getTopSkills(users: UserProfile[]): string[] {
    const skillCounts = new Map<string, number>();
    
    users.forEach(user => {
      if (user.skills) {
        user.skills.forEach(skill => {
          skillCounts.set(skill, (skillCounts.get(skill) || 0) + 1);
        });
      }
    });
    
    return Array.from(skillCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .map(([skill]) => skill);
  }

  private generatePersonStructuredData(user: UserProfile, profileUrl: string): Record<string, any> {
    return {
      "@context": "https://schema.org",
      "@type": "Person",
      "name": user.displayName || 'Developer',
      "jobTitle": user.title || 'Software Developer',
      "description": user.bio || '',
      "url": profileUrl,
      "image": user.photoURL || '',
      "sameAs": [
        user.githubUrl,
        user.linkedinUrl,
        user.twitterUrl,
        user.websiteUrl
      ].filter(Boolean),
      "knowsAbout": user.skills || [],
      "worksFor": user.company ? {
        "@type": "Organization",
        "name": user.company
      } : undefined,
      "address": user.location ? {
        "@type": "PostalAddress",
        "addressLocality": user.location
      } : undefined
    };
  }

  private generatePortfoliosStructuredData(users: UserProfile[], baseUrl: string): Record<string, any> {
    return {
      "@context": "https://schema.org",
      "@type": "CollectionPage",
      "name": "Developer Portfolios",
      "description": "Browse talented developers and their portfolios",
      "url": `${baseUrl}/portfolios`,
      "mainEntity": {
        "@type": "ItemList",
        "numberOfItems": users.length,
        "itemListElement": users.slice(0, 10).map((user, index) => ({
          "@type": "ListItem",
          "position": index + 1,
          "item": {
            "@type": "Person",
            "name": user.displayName || 'Developer',
            "jobTitle": user.title || 'Software Developer',
            "url": `${baseUrl}/profile/${user.username || user.uid}`
          }
        }))
      }
    };
  }

  private generateWebsiteStructuredData(baseUrl: string): Record<string, any> {
    return {
      "@context": "https://schema.org",
      "@type": "WebSite",
      "name": "Developer Portfolios",
      "description": "Showcase your code, find amazing talent",
      "url": baseUrl,
      "potentialAction": {
        "@type": "SearchAction",
        "target": {
          "@type": "EntryPoint",
          "urlTemplate": `${baseUrl}/search?q={search_term_string}`
        },
        "query-input": "required name=search_term_string"
      }
    };
  }

  // Cache SEO metadata
  cacheSEOData(url: string, metadata: SEOMetadata, userAgent?: string): void {
    const cacheKey = this.generateCacheKey(url, userAgent);
    
    this.seoCache.set(cacheKey, {
      metadata,
      timestamp: Date.now(),
      url,
      userAgent
    });
    
    this.cleanupSEOCache();
  }

  // Get cached SEO metadata
  getCachedSEOData(url: string, userAgent?: string): SEOMetadata | null {
    const cacheKey = this.generateCacheKey(url, userAgent);
    const cached = this.seoCache.get(cacheKey);
    
    if (!cached) return null;
    
    const age = Date.now() - cached.timestamp;
    if (age > this.CACHE_DURATION) {
      this.seoCache.delete(cacheKey);
      return null;
    }
    
    return cached.metadata;
  }

  private generateCacheKey(url: string, userAgent?: string): string {
    const base = url.toLowerCase().replace(/[^a-z0-9]/g, '_');
    const agent = userAgent ? userAgent.toLowerCase().includes('bot') ? 'bot' : 'user' : 'user';
    return `${base}_${agent}`;
  }

  private cleanupSEOCache(): void {
    if (this.seoCache.size <= this.MAX_CACHE_SIZE) return;
    
    const entries = Array.from(this.seoCache.entries());
    entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
    
    const toRemove = entries.slice(0, this.seoCache.size - this.MAX_CACHE_SIZE);
    toRemove.forEach(([key]) => this.seoCache.delete(key));
  }

  // Generate sitemap data
  generateSitemapData(users: UserProfile[], baseUrl: string): Array<{
    url: string;
    lastmod: string;
    changefreq: string;
    priority: number;
  }> {
    const sitemap = [];
    
    // Add main pages
    sitemap.push({
      url: baseUrl,
      lastmod: new Date().toISOString(),
      changefreq: 'daily',
      priority: 1.0
    });
    
    sitemap.push({
      url: `${baseUrl}/portfolios`,
      lastmod: new Date().toISOString(),
      changefreq: 'hourly',
      priority: 0.9
    });
    
    // Add user profile pages
    users.forEach(user => {
      const username = user.username || user.uid;
      const lastmod = user.updatedAt ? new Date(user.updatedAt).toISOString() : new Date().toISOString();
      const priority = user.isFeatured ? 0.8 : user.isVerified ? 0.7 : 0.6;
      
      sitemap.push({
        url: `${baseUrl}/profile/${username}`,
        lastmod,
        changefreq: 'weekly',
        priority
      });
    });
    
    return sitemap;
  }
}

// Export singleton instance
export const seoOptimizer = new SEOOptimizer();

// Export types
export type { SEOMetadata, SEOCacheEntry };
