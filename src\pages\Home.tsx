export const dynamic = "force-static"; // ensures it's treated as static
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Laptop, Search, Users, ArrowRight, Sparkles, Star, Crown, CheckCircle, ExternalLink, Code2, Cpu, Brain, Users2, Lightbulb, Building2 } from "lucide-react";
import { Link, useNavigate } from "react-router-dom";
import PricingSection from "@/components/pricing";
import { MagicCard } from "@/components/magicui/magic-card";
import { useState, useEffect, useMemo } from "react";
import { useAllUsers } from '@/hooks/useFirebaseCache';
import { Skeleton } from "@/components/ui/skeleton";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider,
} from "@/components/ui/tooltip";


// User interface for portfolio data
interface User {
  uid: string;
  id: string;
  username: string;
  displayName?: string;
  photoURL?: string;
  bannerURL?: string;
  bio?: string;
  role?: string;
  seniorityLevel?: string;
  techStack?: string[];
  country?: string;
  linkedinUrl?: string;
  githubUrl?: string;
  isFeatured?: boolean;
  isVerified?: boolean;
  availableForWork?: boolean;
  createdAt?: number;
  updatedAt?: number;
}

// Seniority level types and icons (copied from Portfolios.tsx)
type SeniorityLevel =
  | "Junior"
  | "Mid-Level"
  | "Senior"
  | "Lead"
  | "Principal"
  | "Architect"
  | "CTO";

type SeniorityIcon = {
  icon: React.ForwardRefExoticComponent<any>;
  color: string;
};

const seniorityIcons: Record<SeniorityLevel, SeniorityIcon> = {
  Junior: { icon: Code2, color: "text-blue-500" },
  "Mid-Level": { icon: Cpu, color: "text-green-500" },
  Senior: { icon: Brain, color: "text-purple-500" },
  Lead: { icon: Users2, color: "text-amber-500" },
  Principal: { icon: Lightbulb, color: "text-red-500" },
  Architect: { icon: Building2, color: "text-indigo-500" },
  CTO: { icon: Crown, color: "text-yellow-500" },
};

const isSeniorityLevel = (level: string): level is SeniorityLevel => {
  return level in seniorityIcons;
};

const isNewProfile = (createdAt: number | undefined, updatedAt: number | undefined) => {
  if (!createdAt) return false;

  const now = Date.now();
  const twentyFourHours = 24 * 60 * 60 * 1000;
  const oneHour = 60 * 60 * 1000;

  const isRecentlyCreated = now - createdAt < twentyFourHours;

  if (!updatedAt) {
    return isRecentlyCreated;
  }

  const timeBetweenCreateAndUpdate = updatedAt - createdAt;
  const isTrulyNew = isRecentlyCreated && timeBetweenCreateAndUpdate < oneHour;

  return isTrulyNew;
};

const countries = [
  { code: "BD", name: "Bangladesh", flag: "🇧🇩" },
  { code: "US", name: "United States", flag: "🇺🇸" },
  { code: "GB", name: "United Kingdom", flag: "🇬🇧" },
  { code: "CA", name: "Canada", flag: "🇨🇦" },
  { code: "AU", name: "Australia", flag: "🇦🇺" },
  { code: "DE", name: "Germany", flag: "🇩🇪" },
  { code: "FR", name: "France", flag: "🇫🇷" },
  { code: "IN", name: "India", flag: "🇮🇳" },
  { code: "BR", name: "Brazil", flag: "🇧🇷" },
  { code: "JP", name: "Japan", flag: "🇯🇵" },
];

const getCountryInfo = (countryCode: string | undefined) => {
  if (!countryCode) return null;

  let country = countries.find((country) => country.code === countryCode);

  if (!country) {
    country = countries.find(
      (country) => country.name.toLowerCase() === countryCode.toLowerCase()
    );
  }

  return country;
};

export default function Home() {
  const navigate = useNavigate();

  // Use cached hook for all users
  const { data: allUsers, isLoading: isLoadingUsers, fromCache } = useAllUsers();

  // Process users to get 8 for home page showcase
  const portfolioUsers = useMemo(() => {
    if (!allUsers || allUsers.length === 0) return [];

    console.log(`Home: Processing ${allUsers.length} users from ${fromCache ? 'cache' : 'Firebase'}...`);

    // Cast UserProfile to User type
    const users = allUsers as User[];

    // Get featured users first (limit 3)
    const featuredUsers = users
      .filter(user => user.isFeatured)
      .sort((a, b) => (a.displayName || '').localeCompare(b.displayName || ''))
      .slice(0, 3);

    let selectedUsers: User[] = [...featuredUsers];

    // Get verified users (limit 3)
    if (selectedUsers.length < 8) {
      const verifiedUsers = users
        .filter(user => user.isVerified && !user.isFeatured)
        .sort((a, b) => (a.displayName || '').localeCompare(b.displayName || ''))
        .slice(0, 3);

      selectedUsers.push(...verifiedUsers);
    }

    // Fill remaining slots with normal users
    if (selectedUsers.length < 8) {
      const remainingSlots = 8 - selectedUsers.length;
      const normalUsers = users
        .filter(user => !user.isFeatured && !user.isVerified)
        .sort((a, b) => (a.displayName || '').localeCompare(b.displayName || ''))
        .slice(0, remainingSlots);

      selectedUsers.push(...normalUsers);
    }

    // Limit to exactly 8 users
    const result = selectedUsers.slice(0, 8);
    console.log(`Home: Selected ${result.length} users for showcase`);

    return result;
  }, [allUsers, fromCache]);

  const isLoadingPortfolios = isLoadingUsers;

  // Helper function to render seniority icons
  const renderSeniorityIcon = (seniorityLevel: string | undefined) => {
    if (!seniorityLevel || !isSeniorityLevel(seniorityLevel)) return null;

    const { icon: Icon, color } = seniorityIcons[seniorityLevel];
    return (
      <Tooltip>
        <TooltipTrigger asChild>
          <div className="ml-1.5 p-1 rounded-full hover:bg-secondary/50 transition-colors cursor-help">
            <Icon className={`h-5 w-5 ${color}`} />
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <p className="font-medium">{seniorityLevel} Developer</p>
        </TooltipContent>
      </Tooltip>
    );
  };

  // Helper function to render tech stacks
  const renderTechStacks = (user: User) => {
    // Convert techStack map to array if it's not already an array
    const techStackArray = Array.isArray(user.techStack)
      ? user.techStack
      : user.techStack
        ? Object.values(user.techStack).filter(tech => typeof tech === 'string')
        : [];

    const shortTechStacks = techStackArray.filter((tech) => tech.length <= 8);
    const longTechStacks = techStackArray.filter((tech) => tech.length > 8);

    return (
      <>
        <div className="flex flex-wrap justify-center gap-2">
          {shortTechStacks.slice(0, 4).map((skill, i) => (
            <span
              key={`${user.uid}-tech-${i}`}
              className="bg-muted text-foreground px-2 py-1 rounded text-xs font-medium"
            >
              {skill}
            </span>
          ))}
        </div>
        <div className="flex flex-wrap justify-center gap-2">
          {shortTechStacks.slice(4, 5).map((skill, i) => (
            <span
              key={`${user.uid}-tech-${i + 4}`}
              className="bg-muted text-foreground px-2 py-1 rounded text-xs font-medium"
            >
              {skill}
            </span>
          ))}
          {(longTechStacks.length > 0 || shortTechStacks.length > 5) && (
            <span className="bg-orange-100 text-orange-700 px-2 py-1 rounded text-xs font-medium">
              +{longTechStacks.length + Math.max(0, shortTechStacks.length - 5)}{" "}
              more
            </span>
          )}
        </div>
      </>
    );
  };

  return (
    <TooltipProvider>
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-slate-950 dark:via-black dark:to-slate-900 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0">
        {/* Animated Grid Pattern */}
        <div className="absolute inset-0 bg-[linear-gradient(to_right,#00000006_1px,transparent_1px),linear-gradient(to_bottom,#00000006_1px,transparent_1px)] dark:bg-[linear-gradient(to_right,#ffffff06_1px,transparent_1px),linear-gradient(to_bottom,#ffffff06_1px,transparent_1px)] bg-[size:64px_64px] animate-pulse" />
        
        {/* Floating Orbs */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-cyan-500/10 to-indigo-500/10 rounded-full blur-3xl animate-pulse" style={{ animationDuration: '8s' }}></div>
        <div className="absolute top-3/4 right-1/4 w-80 h-80 bg-gradient-to-r from-indigo-500/10 to-purple-500/10 rounded-full blur-3xl animate-pulse" style={{ animationDuration: '12s', animationDelay: '-4s' }}></div>
        
        {/* Subtle Radial Gradients */}
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_20%_80%,#3b82f6_0%,transparent_50%)] opacity-10 dark:opacity-20" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_80%_20%,#06b6d4_0%,transparent_50%)] opacity-10 dark:opacity-20" />
      </div>

      <div className="relative z-10">
        {/* Hero Section */}
        <section className="relative py-10 overflow-hidden">
          <div className="container mx-auto px-6 max-w-7xl">
            <div className="text-center space-y-8">
              {/* Enhanced Badge */}
              <div className="inline-flex items-center gap-2 px-6 py-3 bg-white dark:bg-slate-900 border border-gray-200 dark:border-slate-800 rounded-2xl">
                <Sparkles className="w-4 h-4 text-cyan-500 dark:text-cyan-400" />
                <span className="text-sm font-medium text-gray-700 dark:text-white">
                  Discover amazing developer portfolios
                </span>
                <Star className="w-4 h-4 text-cyan-500 dark:text-cyan-400" />
              </div>

              {/* Enhanced Hero Title */}
              <div className="space-y-6">
                <h1 className="text-5xl md:text-6xl lg:text-8xl font-extralight tracking-tight leading-[0.9]">
                  <span className="inline-block bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 dark:from-white dark:via-gray-100 dark:to-white bg-clip-text text-transparent">
                    Showcase Your
                  </span>
                  <br className="hidden md:block" />
                  <span className="inline-block bg-gradient-to-r from-cyan-600 via-indigo-600 to-indigo-700 dark:from-cyan-400 dark:via-white dark:to-indigo-400 bg-clip-text text-transparent">
                    Developer Portfolio
                  </span>
                </h1>
                
                {/* Enhanced Subtitle */}
                <p className="text-xl md:text-2xl max-w-4xl mx-auto leading-relaxed text-gray-600 dark:text-white/70 font-light">
                  Connect with other developers, showcase your work, and discover
                  new talent. Join our community of passionate developers today.
                </p>
              </div>

              {/* Enhanced CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-6 justify-center items-center pt-8">
                <Link to="/portfolios">
                  <Button
                    size="lg"
                    className="group relative px-10 py-6 text-lg font-medium bg-gradient-to-r from-cyan-500 to-indigo-600 hover:from-cyan-400 hover:to-indigo-500 text-white rounded-2xl shadow-2xl hover:shadow-cyan-500/25 transition-all duration-500 transform hover:scale-105 hover:-translate-y-1 border-0"
                  >
                    <span className="relative z-10 flex items-center gap-3">
                      Browse Portfolios
                      <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
                    </span>
                    <div className="absolute inset-0 bg-gradient-to-r from-cyan-400 to-indigo-500 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-xl"></div>
                  </Button>
                </Link>
                <Link to="/create-profile">
                  <Button
                    size="lg"
                    variant="outline"
                    className="px-10 py-6 text-lg font-medium text-gray-700 dark:text-white border-gray-300 dark:border-white/20 hover:bg-gray-100 dark:hover:bg-white/5 hover:border-gray-400 dark:hover:border-white/30 rounded-2xl backdrop-blur-xl transition-all duration-500 transform hover:scale-105 hover:-translate-y-1 shadow-xl"
                  >
                    Submit Your Portfolio
                  </Button>
                </Link>
              </div>

              {/* Enhanced Feature Cards */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-24 max-w-7xl mx-auto">
                <Card className="group relative border border-gray-200/50 dark:border-white/10 bg-white/80 dark:bg-white/5 backdrop-blur-xl hover:bg-white/90 dark:hover:bg-white/8 transition-all duration-500 hover:shadow-2xl hover:shadow-cyan-500/10 transform hover:-translate-y-2 rounded-3xl overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-br from-cyan-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <CardContent className="relative p-10 flex flex-col items-center text-center space-y-6">
                    <div className="w-20 h-20 rounded-3xl flex items-center justify-center bg-gradient-to-br from-cyan-500/10 to-indigo-500/10 border border-gray-200/50 dark:border-white/10 group-hover:scale-110 group-hover:bg-gradient-to-br group-hover:from-cyan-500/20 group-hover:to-indigo-500/20 transition-all duration-500">
                      <Laptop className="h-10 w-10 text-cyan-600 dark:text-cyan-400" />
                    </div>
                    <div className="space-y-4">
                      <h3 className="text-2xl font-semibold text-gray-900 dark:text-white">
                        Showcase Your Work
                      </h3>
                      <p className="text-lg text-gray-600 dark:text-white/70 font-light leading-relaxed">
                        Submit your portfolio and get discovered by potential
                        employers and collaborators.
                      </p>
                    </div>
                  </CardContent>
                </Card>

                <Card className="group relative border border-gray-200/50 dark:border-white/10 bg-white/80 dark:bg-white/5 backdrop-blur-xl hover:bg-white/90 dark:hover:bg-white/8 transition-all duration-500 hover:shadow-2xl hover:shadow-indigo-500/10 transform hover:-translate-y-2 rounded-3xl overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <CardContent className="relative p-10 flex flex-col items-center text-center space-y-6">
                    <div className="w-20 h-20 rounded-3xl flex items-center justify-center bg-gradient-to-br from-indigo-500/10 to-purple-500/10 border border-gray-200/50 dark:border-white/10 group-hover:scale-110 group-hover:bg-gradient-to-br group-hover:from-indigo-500/20 group-hover:to-purple-500/20 transition-all duration-500">
                      <Users className="h-10 w-10 text-indigo-600 dark:text-indigo-400" />
                    </div>
                    <div className="space-y-4">
                      <h3 className="text-2xl font-semibold text-gray-900 dark:text-white">
                        Connect with Developers
                      </h3>
                      <p className="text-lg text-gray-600 dark:text-white/70 font-light leading-relaxed">
                        Find developers with similar interests and skills to
                        collaborate on projects.
                      </p>
                    </div>
                  </CardContent>
                </Card>

                <Card className="group relative border border-gray-200/50 dark:border-white/10 bg-white/80 dark:bg-white/5 backdrop-blur-xl hover:bg-white/90 dark:hover:bg-white/8 transition-all duration-500 hover:shadow-2xl hover:shadow-purple-500/10 transform hover:-translate-y-2 rounded-3xl overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <CardContent className="relative p-10 flex flex-col items-center text-center space-y-6">
                    <div className="w-20 h-20 rounded-3xl flex items-center justify-center bg-gradient-to-br from-purple-500/10 to-pink-500/10 border border-gray-200/50 dark:border-white/10 group-hover:scale-110 group-hover:bg-gradient-to-br group-hover:from-purple-500/20 group-hover:to-pink-500/20 transition-all duration-500">
                      <Search className="h-10 w-10 text-purple-600 dark:text-purple-400" />
                    </div>
                    <div className="space-y-4">
                      <h3 className="text-2xl font-semibold text-gray-900 dark:text-white">
                        Discover New Talent
                      </h3>
                      <p className="text-lg text-gray-600 dark:text-white/70 font-light leading-relaxed">
                        Browse portfolios by technology stack, seniority level, and
                        more to find the perfect match.
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </section>

        {/* Portfolio Showcase Section */}
        <section className="py-24 relative overflow-hidden bg-gradient-to-b from-transparent via-gray-50/30 dark:via-white/[0.01] to-transparent">
          <div className="container mx-auto px-6 max-w-[1400px]">
            <div className="text-center space-y-8 mb-16">
              <div className="inline-flex items-center gap-2 px-6 py-3 bg-gray-100/80 dark:bg-white/5 backdrop-blur-xl border border-gray-200/50 dark:border-white/10 rounded-2xl shadow-2xl">
                <Users className="w-4 h-4 text-cyan-500 dark:text-cyan-400" />
                <Badge className="text-sm font-medium bg-transparent text-gray-700 dark:text-white/90 border-0 px-0">
                  Featured Developers
                </Badge>
              </div>
              <div className="space-y-6">
                <h2 className="text-4xl md:text-5xl lg:text-6xl font-extralight tracking-tight leading-[0.9]">
                  <span className="bg-gradient-to-r from-gray-900 to-gray-700 dark:from-white dark:to-white/80 bg-clip-text text-transparent">
                    Discover Amazing
                  </span>
                  <br className="hidden md:block" />
                  <span className="bg-gradient-to-r from-cyan-600 to-indigo-600 dark:from-cyan-400 dark:to-indigo-400 bg-clip-text text-transparent">
                    Developer Portfolios
                  </span>
                </h2>
                <p className="text-xl max-w-3xl mx-auto text-gray-600 dark:text-white/70 font-light leading-relaxed">
                  Meet talented developers from our community showcasing their best work
                </p>
              </div>
            </div>

            {/* Portfolio Grid */}
            {isLoadingPortfolios ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8 py-8 justify-center items-start">
                {[...Array(8)].map((_, index) => (
                  <MagicCard
                    key={index}
                    className="max-w-xs w-full flex flex-col items-center overflow-visible p-0 h-auto transition-all rounded-2xl border border-border"
                  >
                    <div className="relative w-full">
                      <Skeleton className="w-full h-32 rounded-t-2xl" />
                      <div className="absolute left-1/2 top-full -translate-x-1/2 -translate-y-1/2">
                        <Skeleton className="w-24 h-24 rounded-full border-4 border-white shadow" />
                      </div>
                    </div>
                    <div className="mt-12 w-full flex flex-col items-center px-6 flex-1">
                      <div className="flex items-center gap-1 mb-1">
                        <Skeleton className="h-6 w-32" />
                        <Skeleton className="h-4 w-4 rounded-full" />
                      </div>
                      <div className="flex items-center gap-2 mb-2">
                        <Skeleton className="h-4 w-20" />
                        <Skeleton className="h-5 w-5 rounded-full" />
                      </div>
                      <Skeleton className="h-12 w-full mb-4" />
                      <div className="flex flex-col gap-2 mb-4 w-full">
                        <div className="flex flex-wrap justify-center gap-2">
                          <Skeleton className="h-6 w-16" />
                          <Skeleton className="h-6 w-16" />
                          <Skeleton className="h-6 w-16" />
                          <Skeleton className="h-6 w-16" />
                        </div>
                        <div className="flex flex-wrap justify-center gap-2">
                          <Skeleton className="h-6 w-16" />
                          <Skeleton className="h-6 w-20" />
                        </div>
                      </div>
                      <Skeleton className="h-10 w-[200px] mt-auto mb-4" />
                    </div>
                  </MagicCard>
                ))}
              </div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8 py-8 justify-center items-start">
                {portfolioUsers.map((user) => (
                  <MagicCard
                    key={`home-user-${user.uid}`}
                    className={`max-w-xs w-full flex flex-col items-center overflow-visible p-0 h-auto transition-all rounded-2xl ${
                      user.isFeatured
                        ? "border-2 border-[#F65100] hover:scale-[1.02] transform transition-all duration-300 relative"
                        : "border border-border hover:border-primary relative"
                    }`}
                  >
                    <div className="relative w-full">
                      {/* New Developer badge at top-right if present */}
                      {isNewProfile(user.createdAt, user.updatedAt) && (
                        <div className="absolute top-2 right-2 z-20">
                          <span className="bg-emerald-500/90 text-white px-3 py-2 rounded-md text-xs font-medium shadow-sm flex items-center gap-1.5 border border-emerald-400/20 backdrop-blur-sm">
                            New Developer
                          </span>
                        </div>
                      )}
                      {/* Available badge: bottom-right if New Developer, else top-right */}
                      {user.availableForWork && (
                        isNewProfile(user.createdAt, user.updatedAt) ? (
                          <div className="absolute bottom-2 right-2 z-20">
                            <span className="bg-green-500 text-white px-3 py-1 rounded-full text-xs font-semibold shadow flex items-center gap-1">
                              <CheckCircle className="h-4 w-4 text-white" />
                              Available
                            </span>
                          </div>
                        ) : (
                          <div className="absolute top-2 right-2 z-20">
                            <span className="bg-green-500 text-white px-3 py-1 rounded-full text-xs font-semibold shadow flex items-center gap-1">
                              <CheckCircle className="h-4 w-4 text-white" />
                              Available
                            </span>
                          </div>
                        )
                      )}
                      {/* Featured badge remains at top-left, but offset if needed */}
                      {user.isFeatured && (
                        <div className="absolute top-2 left-2 z-10" style={{ marginTop: isNewProfile(user.createdAt, user.updatedAt) ? '2.5rem' : undefined }}>
                          <span className="bg-orange-500 text-white px-4 py-2 rounded-full text-xs font-semibold shadow-lg flex items-center gap-1.5">
                            <Star className="h-3.5 w-3.5" />
                            Featured Developer
                          </span>
                        </div>
                      )}
                      <img
                        src={user.bannerURL || "/placeholder-cover.jpg"}
                        alt="Banner"
                        className="w-full h-32 object-cover rounded-t-2xl"
                      />
                      <div className="absolute left-1/2 top-full -translate-x-1/2 -translate-y-1/2">
                        <div className="relative w-24 h-24">
                          <img
                            src={user.photoURL || "/placeholder-avatar.jpg"}
                            alt={user.displayName || user.username}
                            className={`w-24 h-24 rounded-full object-cover border-4 ${
                              user.isFeatured
                                ? "border-orange-500"
                                : "border-white"
                            } shadow bg-transparent`}
                          />
                          {user.isFeatured && (
                            <span className="absolute bottom-0 right-0 bg-orange-500 rounded-full p-1 shadow-lg">
                              <Crown className="h-4 w-4 text-white" />
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="mt-12 w-full flex flex-col items-center px-6 flex-1">
                      <h3 className="text-xl font-bold text-center mb-1 w-full flex items-center justify-center gap-1 text-foreground">
                        <span>{user.displayName || user.username}</span>
                        {user.isVerified && (
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="16"
                            height="16"
                            className="ml-1 inline-block align-super"
                            viewBox="0 0 48 48"
                          >
                            <polygon
                              fill="#42a5f5"
                              points="29.62,3 33.053,8.308 39.367,8.624 39.686,14.937 44.997,18.367 42.116,23.995 45,29.62 39.692,33.053 39.376,39.367 33.063,39.686 29.633,44.997 24.005,42.116 18.38,45 14.947,39.692 8.633,39.376 8.314,33.063 3.003,29.633 5.884,24.005 3,18.38 8.308,14.947 8.624,8.633 14.937,8.314 18.367,3.003 23.995,5.884"
                            />
                            <polygon
                              fill="#fff"
                              points="21.396,31.255 14.899,24.76 17.021,22.639 21.428,27.046 30.996,17.772 33.084,19.926"
                            />
                          </svg>
                        )}
                        {user.country && getCountryInfo(user.country) && (
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <div className="ml-1">
                                <span className="text-lg">
                                  {getCountryInfo(user.country)?.flag}
                                </span>
                              </div>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p className="font-medium">
                                {getCountryInfo(user.country)?.name}
                              </p>
                            </TooltipContent>
                          </Tooltip>
                        )}
                      </h3>
                      <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground mb-2">
                        <span>{user.role || "Developer"}</span>
                        {renderSeniorityIcon(user.seniorityLevel)}
                      </div>
                      <p className="text-center text-foreground mb-4 line-clamp-2">
                        {user.bio ||
                          "A passionate developer building amazing applications."}
                      </p>
                      <div className="flex flex-col gap-2 mb-4">
                        {renderTechStacks(user)}
                      </div>
                      <div className="flex items-center justify-center gap-4 mb-4">
                        {user.linkedinUrl && (
                          <a
                            href={user.linkedinUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-muted-foreground hover:text-foreground transition-colors flex items-center gap-1.5"
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="16"
                              height="16"
                              viewBox="0 0 24 24"
                              fill="currentColor"
                            >
                              <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" />
                            </svg>
                            LinkedIn
                          </a>
                        )}
                        {user.githubUrl && (
                          <a
                            href={user.githubUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-muted-foreground hover:text-foreground transition-colors flex items-center gap-1.5"
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="16"
                              height="16"
                              viewBox="0 0 24 24"
                              fill="currentColor"
                            >
                              <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z" />
                            </svg>
                            GitHub
                          </a>
                        )}
                      </div>
                    </div>
                    <button
                      className={`${
                        user.isFeatured
                          ? "bg-orange-500 hover:bg-orange-600 shadow-lg hover:shadow-orange-500/25"
                          : "bg-background text-foreground border border-border hover:bg-accent hover:text-accent-foreground"
                      } py-2.5 rounded-lg font-semibold transition-all duration-300 mt-auto mb-4 mx-auto px-8 w-70 shadow-lg flex items-center justify-center gap-2 hover:scale-105`}
                      onClick={() => navigate(`/${user.username}`)}
                    >
                      <span>
                        {user.isFeatured
                          ? "View Premium Profile"
                          : "View Profile"}
                      </span>
                      <ExternalLink className="h-4 w-4" />
                    </button>
                  </MagicCard>
                ))}
              </div>
            )}

            {/* See All Portfolios Button */}
            <div className="text-center">
              <Link to="/portfolios">
                <Button
                  size="lg"
                  className="group relative px-10 py-6 text-lg font-medium bg-gradient-to-r from-cyan-500 to-indigo-600 hover:from-cyan-400 hover:to-indigo-500 text-white rounded-2xl shadow-2xl hover:shadow-cyan-500/25 transition-all duration-500 transform hover:scale-105 hover:-translate-y-1 border-0"
                >
                  <span className="relative z-10 flex items-center gap-3">
                    See All Portfolios
                    <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
                  </span>
                  <div className="absolute inset-0 bg-gradient-to-r from-cyan-400 to-indigo-500 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-xl"></div>
                </Button>
              </Link>
            </div>
          </div>
        </section>

        {/* How It Works Section - Vibrant Gradient UI */}
        <section className="py-24 relative overflow-hidden bg-gradient-to-b from-cyan-50 via-indigo-50 to-purple-50 dark:from-slate-950 dark:via-black dark:to-slate-900 border-t border-b border-gray-100 dark:border-white/10">
          <div className="container mx-auto px-6 max-w-5xl">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-extralight tracking-tight leading-[0.9] bg-gradient-to-r from-gray-900 to-gray-700 dark:from-white dark:to-white/80 bg-clip-text text-transparent mb-2">
                Create Your Portfolio in Three Simple Steps
              </h2>
              <p className="text-xl max-w-2xl mx-auto text-gray-600 dark:text-white/70 font-light leading-relaxed">
                Showcase your skills and join our developer community in just a few minutes.
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
              {/* Step 1 */}
              <div className="group relative border border-gray-200/50 dark:border-white/10 bg-white/80 dark:bg-white/5 backdrop-blur-xl hover:bg-white/90 dark:hover:bg-white/8 transition-all duration-500 hover:shadow-2xl hover:shadow-cyan-500/10 transform hover:-translate-y-2 rounded-3xl overflow-hidden p-8 flex flex-col items-center text-center">
                <div className="flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br from-cyan-500/10 to-indigo-500/10 border border-gray-200/50 dark:border-white/10 mb-4 group-hover:scale-110 transition-all duration-500">
                  <svg className="w-8 h-8 text-cyan-600 dark:text-cyan-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" /></svg>
                </div>
                <span className="text-sm font-semibold text-cyan-600 dark:text-cyan-400 mb-1">Step 1</span>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">Sign Up</h3>
                <p className="text-gray-600 dark:text-white/70 font-light">Create your account and get your unique portfolio URL instantly.</p>
              </div>
              {/* Step 2 */}
              <div className="group relative border border-gray-200/50 dark:border-white/10 bg-white/80 dark:bg-white/5 backdrop-blur-xl hover:bg-white/90 dark:hover:bg-white/8 transition-all duration-500 hover:shadow-2xl hover:shadow-indigo-500/10 transform hover:-translate-y-2 rounded-3xl overflow-hidden p-8 flex flex-col items-center text-center">
                <div className="flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br from-indigo-500/10 to-purple-500/10 border border-gray-200/50 dark:border-white/10 mb-4 group-hover:scale-110 transition-all duration-500">
                  <svg className="w-8 h-8 text-indigo-600 dark:text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" /></svg>
                </div>
                <span className="text-sm font-semibold text-indigo-600 dark:text-indigo-400 mb-1">Step 2</span>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">Customize</h3>
                <p className="text-gray-600 dark:text-white/70 font-light">Add your projects, skills, and connect your social media profiles.</p>
              </div>
              {/* Step 3 */}
              <div className="group relative border border-gray-200/50 dark:border-white/10 bg-white/80 dark:bg-white/5 backdrop-blur-xl hover:bg-white/90 dark:hover:bg-white/8 transition-all duration-500 hover:shadow-2xl hover:shadow-purple-500/10 transform hover:-translate-y-2 rounded-3xl overflow-hidden p-8 flex flex-col items-center text-center">
                <div className="flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br from-purple-500/10 to-pink-500/10 border border-gray-200/50 dark:border-white/10 mb-4 group-hover:scale-110 transition-all duration-500">
                  <svg className="w-8 h-8 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" /></svg>
                </div>
                <span className="text-sm font-semibold text-purple-600 dark:text-purple-400 mb-1">Step 3</span>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">Go Premium</h3>
                <p className="text-gray-600 dark:text-white/70 font-light">Unlock advanced features and get featured in our developer showcase.</p>
              </div>
            </div>
            <div className="text-center">
              <Link to="/create-profile">
                <Button size="lg" className="group relative px-10 py-6 text-lg font-medium bg-gradient-to-r from-cyan-500 to-indigo-600 hover:from-cyan-400 hover:to-indigo-500 text-white rounded-2xl shadow-2xl hover:shadow-cyan-500/25 transition-all duration-500 transform hover:scale-105 hover:-translate-y-1 border-0">
                  <span className="relative z-10 flex items-center gap-3">
                    Start Creating Your Portfolio
                    <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
                  </span>
                  <div className="absolute inset-0 bg-gradient-to-r from-cyan-400 to-indigo-500 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-xl"></div>
                </Button>
              </Link>
            </div>
          </div>
        </section>

          <PricingSection />


        {/* Enhanced Footer Section */}
        <footer className="bg-gradient-to-t from-gray-100/80 dark:from-white/[0.03] to-transparent border-t border-gray-200/50 dark:border-white/10 backdrop-blur-xl">
          <div className="container mx-auto px-6 py-16 max-w-7xl">
            <div className="flex flex-col items-center justify-center space-y-12">
              {/* Enhanced Logo and Description */}
              <div className="flex flex-col items-center space-y-8 text-center">
                <h3 className="text-3xl font-light text-gray-900 dark:text-white">
                  DevPortfolio
                </h3>
                <p className="text-lg text-gray-600 dark:text-white/70 font-light max-w-2xl leading-relaxed">
                  The best platform for developers to showcase their work and
                  connect with opportunities.
                </p>
                <div className="flex space-x-8">
                  <a
                    href="#"
                    className="w-12 h-12 rounded-2xl bg-gray-100/80 dark:bg-white/5 border border-gray-200/50 dark:border-white/10 flex items-center justify-center text-gray-600 dark:text-white/70 hover:text-gray-900 dark:hover:text-white hover:bg-gray-200/80 dark:hover:bg-white/10 transition-all duration-300 backdrop-blur-xl"
                  >
                    <svg
                      className="w-6 h-6"
                      fill="currentColor"
                      viewBox="0 0 24 24"
                      aria-hidden="true"
                    >
                      <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                    </svg>
                  </a>
                  <a
                    href="#"
                    className="w-12 h-12 rounded-2xl bg-gray-100/80 dark:bg-white/5 border border-gray-200/50 dark:border-white/10 flex items-center justify-center text-gray-600 dark:text-white/70 hover:text-gray-900 dark:hover:text-white hover:bg-gray-200/80 dark:hover:bg-white/10 transition-all duration-300 backdrop-blur-xl"
                  >
                    <svg
                      className="w-6 h-6"
                      fill="currentColor"
                      viewBox="0 0 24 24"
                      aria-hidden="true"
                    >
                      <path
                        fillRule="evenodd"
                        d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </a>
                </div>
              </div>

              {/* Enhanced Copyright */}
              <div className="pt-8 border-t border-gray-200/50 dark:border-white/10 w-full text-center">
                <p className="text-gray-500 dark:text-white/50 font-light">
                  © {new Date().getFullYear()} Sera programmer. All rights
                  reserved.
                </p>
              </div>
            </div>
          </div>
        </footer>
      </div>
    </div>
    </TooltipProvider>
  );
}
