import React, { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { 
  Activity, 
  TrendingUp, 
  Clock, 
  Database, 
  AlertTriangle, 
  CheckCircle,
  Download,
  RefreshCw
} from 'lucide-react';
import { cacheAnalytics, type CacheMetrics, type CacheAlert } from '@/services/cache-analytics';

interface CacheAnalyticsDashboardProps {
  className?: string;
}

export const CacheAnalyticsDashboard: React.FC<CacheAnalyticsDashboardProps> = ({ 
  className = '' 
}) => {
  const [metrics, setMetrics] = useState<CacheMetrics | null>(null);
  const [alerts, setAlerts] = useState<CacheAlert[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [timeWindow, setTimeWindow] = useState<number>(60 * 60 * 1000); // 1 hour

  const refreshData = () => {
    setIsLoading(true);
    try {
      const newMetrics = cacheAnalytics.getMetrics(timeWindow);
      const newAlerts = cacheAnalytics.getAlerts();
      
      setMetrics(newMetrics);
      setAlerts(newAlerts);
    } catch (error) {
      console.error('Failed to load analytics data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    refreshData();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(refreshData, 30000);
    return () => clearInterval(interval);
  }, [timeWindow]);

  const exportData = () => {
    const data = cacheAnalytics.exportData();
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `cache-analytics-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDuration = (ms: number): string => {
    if (ms < 1000) return `${ms.toFixed(0)}ms`;
    return `${(ms / 1000).toFixed(2)}s`;
  };

  if (isLoading || !metrics) {
    return (
      <div className={`p-6 ${className}`}>
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading analytics...</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`p-6 space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Cache Analytics Dashboard</h2>
          <p className="text-muted-foreground">
            Monitor cache performance and optimize data access patterns
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <select 
            value={timeWindow} 
            onChange={(e) => setTimeWindow(Number(e.target.value))}
            className="px-3 py-2 border rounded-md"
          >
            <option value={60 * 60 * 1000}>Last Hour</option>
            <option value={24 * 60 * 60 * 1000}>Last 24 Hours</option>
            <option value={7 * 24 * 60 * 60 * 1000}>Last Week</option>
          </select>
          <Button onClick={refreshData} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button onClick={exportData} variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Alerts */}
      {alerts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2 text-yellow-500" />
              Active Alerts ({alerts.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {alerts.slice(0, 5).map((alert) => (
                <div key={alert.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-2">
                    <Badge variant={alert.type === 'error' ? 'destructive' : 'secondary'}>
                      {alert.type}
                    </Badge>
                    <span>{alert.message}</span>
                  </div>
                  <span className="text-sm text-muted-foreground">
                    {new Date(alert.timestamp).toLocaleTimeString()}
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Hit Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.hitRate.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">
              {metrics.hits} hits / {metrics.totalRequests} requests
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Response Time</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatDuration(metrics.averageResponseTime)}</div>
            <p className="text-xs text-muted-foreground">
              Average cache lookup time
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Cache Size</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatBytes(metrics.cacheSize)}</div>
            <p className="text-xs text-muted-foreground">
              Total cached data
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Data Transferred</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatBytes(metrics.totalDataTransferred)}</div>
            <p className="text-xs text-muted-foreground">
              Total data served from cache
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics */}
      <Tabs defaultValue="performance" className="w-full">
        <TabsList>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="keys">Top Keys</TabsTrigger>
          <TabsTrigger value="timeline">Timeline</TabsTrigger>
        </TabsList>

        <TabsContent value="performance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Cache Performance Overview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-semibold mb-2">Cache Efficiency</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span>Hit Rate:</span>
                        <span className={metrics.hitRate > 70 ? 'text-green-600' : metrics.hitRate > 50 ? 'text-yellow-600' : 'text-red-600'}>
                          {metrics.hitRate.toFixed(1)}%
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span>Total Requests:</span>
                        <span>{metrics.totalRequests}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Cache Hits:</span>
                        <span className="text-green-600">{metrics.hits}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Cache Misses:</span>
                        <span className="text-red-600">{metrics.misses}</span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">Performance Metrics</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span>Avg Response Time:</span>
                        <span>{formatDuration(metrics.averageResponseTime)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Cache Size:</span>
                        <span>{formatBytes(metrics.cacheSize)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Data Transferred:</span>
                        <span>{formatBytes(metrics.totalDataTransferred)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="keys" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Top Performing Cache Keys</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {metrics.topKeys.map((key, index) => (
                  <div key={key.key} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <Badge variant="outline">#{index + 1}</Badge>
                      <span className="font-mono text-sm">{key.key}</span>
                    </div>
                    <div className="flex items-center space-x-4 text-sm">
                      <span className="text-green-600">{key.hits} hits</span>
                      <span className="text-red-600">{key.misses} misses</span>
                      <Badge variant={key.hitRate > 70 ? 'default' : key.hitRate > 50 ? 'secondary' : 'destructive'}>
                        {key.hitRate.toFixed(1)}%
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="timeline" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Cache Performance Timeline</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {metrics.timeSeriesData.slice(-12).map((dataPoint, index) => (
                  <div key={index} className="flex items-center justify-between p-2 border-b">
                    <span className="text-sm">
                      {new Date(dataPoint.timestamp).toLocaleTimeString()}
                    </span>
                    <div className="flex items-center space-x-4 text-sm">
                      <span className="text-green-600">{dataPoint.hits} hits</span>
                      <span className="text-red-600">{dataPoint.misses} misses</span>
                      <Badge variant={dataPoint.hitRate > 70 ? 'default' : 'secondary'}>
                        {dataPoint.hitRate.toFixed(1)}%
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default CacheAnalyticsDashboard;
