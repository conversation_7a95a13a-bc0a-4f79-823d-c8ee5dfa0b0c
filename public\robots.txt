# Robots.txt for Sera Programmer - Developer Portfolio Platform
# Updated: 2024-06-19
# Enhanced for SEO optimization

# Allow all search engines to crawl the site
User-agent: *
Allow: /

# Specific rules for major search engines
User-agent: Googlebot
Allow: /
Crawl-delay: 1

User-agent: Bingbot
Allow: /
Crawl-delay: 1

User-agent: Slurp
Allow: /
Crawl-delay: 2

User-agent: DuckDuckBot
Allow: /
Crawl-delay: 1

User-agent: Bai<PERSON><PERSON>er
Allow: /
Crawl-delay: 2

User-agent: YandexBot
Allow: /
Crawl-delay: 2

User-agent: facebookexternalhit
Allow: /

User-agent: Twitterbot
Allow: /

User-agent: LinkedInBot
Allow: /

# Disallow sensitive or unnecessary paths
Disallow: /admin/
Disallow: /api/
Disallow: /private/
Disallow: /.env
Disallow: /config/
Disallow: /logs/
Disallow: /temp/
Disallow: /cache/
Disallow: /backup/
Disallow: /test/
Disallow: /dev/
Disallow: /*.json$
Disallow: /*.xml$
Disallow: /*.txt$
Disallow: /search?*
Disallow: /*?utm_*
Disallow: /*?ref=*
Disallow: /*?source=*
Disallow: /*?campaign=*

# Allow important files for SEO
Allow: /sitemap.xml
Allow: /sitemap*.xml
Allow: /robots.txt
Allow: /favicon.ico
Allow: /.well-known/
Allow: /opensearch.xml
Allow: /rss.xml
Allow: /atom.xml

# Sitemap locations
Sitemap: https://seraprogrammer.com/sitemap.xml
Sitemap: https://seraprogrammer.com/sitemap-portfolios.xml
Sitemap: https://seraprogrammer.com/sitemap-users.xml
Sitemap: https://seraprogrammer.com/sitemap-blog.xml

# Host directive (helps with canonicalization)
Host: https://seraprogrammer.com

# Clean-param directive for Yandex (removes tracking parameters)
Clean-param: utm_source&utm_medium&utm_campaign&utm_term&utm_content&ref&source&campaign&gclid&fbclid
