import React, { createContext, useContext, useEffect, useState, type ReactNode } from 'react';
import {
  getCacheStats,
  resetCacheStats,
  clearUserCache,
  clearCacheByPattern,
  invalidateOnUserUpdate,
  invalidateOnNewUser,
  cleanupExpiredEntries,
  startAutomaticCleanup,
  stopAutomaticCleanup,
  useCachePerformance,
  type UserProfile
} from '@/hooks/useFirebaseCache';
import { cachePreloader, type PreloadResult } from '@/services/cache-preloader';
import { collection, getDocs } from 'firebase/firestore';
import { db } from '@/config/firebase';

interface CacheContextType {
  // Cache statistics and monitoring
  stats: ReturnType<typeof getCacheStats>;
  refreshStats: () => void;
  resetStats: () => void;

  // Cache management
  clearCache: () => void;
  clearUserCache: (username?: string) => void;
  clearCacheByPattern: (pattern: string) => void;

  // Smart cache invalidation
  invalidateOnUserUpdate: (username: string) => void;
  invalidateOnNewUser: () => void;
  cleanupExpiredEntries: () => number;

  // Cache preloading
  preloadCache: () => Promise<void>;
  preloadUsers: () => Promise<void>;
  preloadFeaturedUsers: () => Promise<void>;
  preloadAll: () => Promise<PreloadResult[]>;
  preloadCritical: () => Promise<PreloadResult[]>;
  smartPreload: (behavior: {
    visitedPortfolios: boolean;
    searchedUsers: boolean;
    viewedProfiles: string[];
  }) => Promise<void>;

  // Cache status
  isPreloading: boolean;
  lastPreloadTime: number | null;
  preloadResults: PreloadResult[];

  // Cache health monitoring
  cacheHealth: {
    isHealthy: boolean;
    issues: string[];
    recommendations: string[];
  };
}

const CacheContext = createContext<CacheContextType | undefined>(undefined);

interface CacheProviderProps {
  children: ReactNode;
}

export const CacheProvider: React.FC<CacheProviderProps> = ({ children }) => {
  const { stats, refreshStats, clearCache, preloadCache, resetStats } = useCachePerformance();
  const [isPreloading, setIsPreloading] = useState(false);
  const [lastPreloadTime, setLastPreloadTime] = useState<number | null>(null);
  const [preloadResults, setPreloadResults] = useState<PreloadResult[]>([]);

  // Preload users specifically
  const preloadUsers = async () => {
    setIsPreloading(true);
    try {
      await preloadCache();
      setLastPreloadTime(Date.now());
    } catch (error) {
      console.error('Failed to preload users:', error);
    } finally {
      setIsPreloading(false);
    }
  };

  // Enhanced preloading functions using the preloader service
  const preloadAll = async (): Promise<PreloadResult[]> => {
    setIsPreloading(true);
    try {
      const results = await cachePreloader.preloadAll();
      setPreloadResults(results);
      setLastPreloadTime(Date.now());
      refreshStats();
      return results;
    } catch (error) {
      console.error('Failed to preload all data:', error);
      return [];
    } finally {
      setIsPreloading(false);
    }
  };

  const preloadCritical = async (): Promise<PreloadResult[]> => {
    setIsPreloading(true);
    try {
      const results = await cachePreloader.preloadCritical();
      setPreloadResults(results);
      setLastPreloadTime(Date.now());
      refreshStats();
      return results;
    } catch (error) {
      console.error('Failed to preload critical data:', error);
      return [];
    } finally {
      setIsPreloading(false);
    }
  };

  const smartPreload = async (behavior: {
    visitedPortfolios: boolean;
    searchedUsers: boolean;
    viewedProfiles: string[];
  }) => {
    setIsPreloading(true);
    try {
      await cachePreloader.smartPreload(behavior);
      setLastPreloadTime(Date.now());
      refreshStats();
    } catch (error) {
      console.error('Failed to smart preload:', error);
    } finally {
      setIsPreloading(false);
    }
  };

  // Preload featured users
  const preloadFeaturedUsers = async () => {
    setIsPreloading(true);
    try {
      console.log('Preloading featured users...');

      // This would typically use a cached hook, but for preloading we'll do direct fetch
      const featuredQuery = collection(db, 'users');
      const snapshot = await getDocs(featuredQuery);

      console.log(`Preloaded featured users data`);
      setLastPreloadTime(Date.now());
    } catch (error) {
      console.error('Failed to preload featured users:', error);
    } finally {
      setIsPreloading(false);
    }
  };

  // Analyze cache health
  const cacheHealth = React.useMemo(() => {
    const issues: string[] = [];
    const recommendations: string[] = [];
    
    // Check hit rate
    if (stats.hitRate < 50 && stats.totalHits + stats.totalMisses > 10) {
      issues.push('Low cache hit rate');
      recommendations.push('Consider increasing cache duration or preloading frequently accessed data');
    }
    
    // Check cache size
    if (stats.totalEntries > stats.maxSize * 0.8) {
      issues.push('Cache approaching maximum size');
      recommendations.push('Consider increasing MAX_CACHE_SIZE or implementing better cleanup strategies');
    }
    
    // Check for expired entries
    const expiredEntries = stats.entries.filter(entry => entry.isExpired).length;
    if (expiredEntries > stats.totalEntries * 0.3) {
      issues.push('Many expired entries in cache');
      recommendations.push('Consider running cache cleanup more frequently');
    }
    
    // Check for error entries
    const errorEntries = stats.entries.filter(entry => entry.hasError).length;
    if (errorEntries > 0) {
      issues.push(`${errorEntries} cached error entries`);
      recommendations.push('Review and clear error entries that may be preventing successful requests');
    }
    
    return {
      isHealthy: issues.length === 0,
      issues,
      recommendations
    };
  }, [stats]);

  // Auto-preload on app start with intelligent strategy
  useEffect(() => {
    const shouldPreload = !lastPreloadTime || (Date.now() - lastPreloadTime) > 30 * 60 * 1000; // 30 minutes

    if (shouldPreload) {
      if (process.env.NODE_ENV === 'development') {
        console.log('Auto-preloading critical cache on app start...');
      }

      // Use critical preload for faster initial load
      preloadCritical().then(results => {
        if (process.env.NODE_ENV === 'development') {
          console.log('Critical preload completed:', results);
        }

        // Schedule full preload after a delay to not block initial rendering
        setTimeout(() => {
          if (!cachePreloader.isCurrentlyPreloading()) {
            if (process.env.NODE_ENV === 'development') {
              console.log('Starting background full preload...');
            }
            preloadAll();
          }
        }, 5000); // 5 second delay
      });
    }

    // Start automatic cleanup
    startAutomaticCleanup(5 * 60 * 1000); // Every 5 minutes

    // Cleanup on unmount
    return () => {
      stopAutomaticCleanup();
    };
  }, []);

  // Periodic cache health monitoring
  useEffect(() => {
    const interval = setInterval(() => {
      refreshStats();
      
      // Log cache health issues
      if (!cacheHealth.isHealthy) {
        console.warn('Cache health issues detected:', cacheHealth.issues);
        console.info('Recommendations:', cacheHealth.recommendations);
      }
    }, 60000); // Check every minute

    return () => clearInterval(interval);
  }, [refreshStats, cacheHealth]);

  const value: CacheContextType = {
    stats,
    refreshStats,
    resetStats,
    clearCache,
    clearUserCache,
    clearCacheByPattern,
    invalidateOnUserUpdate,
    invalidateOnNewUser,
    cleanupExpiredEntries,
    preloadCache,
    preloadUsers,
    preloadFeaturedUsers,
    preloadAll,
    preloadCritical,
    smartPreload,
    isPreloading,
    lastPreloadTime,
    preloadResults,
    cacheHealth
  };

  return (
    <CacheContext.Provider value={value}>
      {children}
    </CacheContext.Provider>
  );
};

// Custom hook to use cache context
export const useCache = (): CacheContextType => {
  const context = useContext(CacheContext);
  if (context === undefined) {
    throw new Error('useCache must be used within a CacheProvider');
  }
  return context;
};

// Cache debugging component (for development)
export const CacheDebugger: React.FC = () => {
  const {
    stats,
    cacheHealth,
    refreshStats,
    clearCache,
    preloadUsers,
    preloadAll,
    preloadCritical,
    isPreloading,
    preloadResults,
    lastPreloadTime
  } = useCache();

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  const successfulPreloads = preloadResults.filter(r => r.success).length;
  const totalPreloadTime = preloadResults.reduce((sum, r) => sum + r.duration, 0);

  return (
    <div className="fixed bottom-4 right-4 bg-black/90 text-white p-4 rounded-lg text-xs max-w-sm max-h-96 overflow-y-auto">
      <div className="font-bold mb-2">Cache Debug Info</div>

      {/* Cache Stats */}
      <div className="mb-3">
        <div>Entries: {stats.totalEntries}/{stats.maxSize}</div>
        <div>Hit Rate: {stats.hitRate}%</div>
        <div>Hits: {stats.totalHits} | Misses: {stats.totalMisses}</div>
      </div>

      {/* Cache Health */}
      <div className={`mb-3 ${cacheHealth.isHealthy ? 'text-green-400' : 'text-red-400'}`}>
        Health: {cacheHealth.isHealthy ? 'Good' : 'Issues'}
      </div>
      {cacheHealth.issues.length > 0 && (
        <div className="text-yellow-400 text-xs mb-3">
          {cacheHealth.issues.join(', ')}
        </div>
      )}

      {/* Preload Info */}
      <div className="mb-3 border-t border-gray-600 pt-2">
        <div className="font-semibold mb-1">Preload Status</div>
        {lastPreloadTime && (
          <div>Last: {new Date(lastPreloadTime).toLocaleTimeString()}</div>
        )}
        {preloadResults.length > 0 && (
          <div>
            <div>Results: {successfulPreloads}/{preloadResults.length} ({totalPreloadTime}ms)</div>
            <div className="mt-1 space-y-1">
              {preloadResults.slice(-3).map((result, i) => (
                <div key={i} className={`text-xs ${result.success ? 'text-green-300' : 'text-red-300'}`}>
                  {result.strategy}: {result.success ? '✓' : '✗'} ({result.duration}ms)
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Control Buttons */}
      <div className="space-y-2">
        <div className="flex space-x-1">
          <button
            onClick={refreshStats}
            className="bg-blue-600 px-2 py-1 rounded text-xs flex-1"
          >
            Refresh
          </button>
          <button
            onClick={clearCache}
            className="bg-red-600 px-2 py-1 rounded text-xs flex-1"
          >
            Clear
          </button>
        </div>
        <div className="flex space-x-1">
          <button
            onClick={preloadCritical}
            disabled={isPreloading}
            className="bg-orange-600 px-2 py-1 rounded text-xs disabled:opacity-50 flex-1"
          >
            {isPreloading ? 'Loading...' : 'Critical'}
          </button>
          <button
            onClick={preloadAll}
            disabled={isPreloading}
            className="bg-green-600 px-2 py-1 rounded text-xs disabled:opacity-50 flex-1"
          >
            {isPreloading ? 'Loading...' : 'All'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default CacheContext;
