import React, { createContext, useContext, useEffect, useState, type ReactNode } from 'react';
import {
  getCacheStats,
  resetCacheStats,
  clearUserCache,
  clearCacheByPattern,
  useCachePerformance,
  type UserProfile
} from '@/hooks/useFirebaseCache';
import { cachePreloader, type PreloadResult } from '@/services/cache-preloader';
import { collection, getDocs } from 'firebase/firestore';
import { db } from '@/config/firebase';

interface CacheContextType {
  // Cache statistics and monitoring
  stats: ReturnType<typeof getCacheStats>;
  refreshStats: () => void;
  resetStats: () => void;

  // Cache management
  clearCache: () => void;
  clearUserCache: (username?: string) => void;
  clearCacheByPattern: (pattern: string) => void;

  // Cache preloading
  preloadCache: () => Promise<void>;
  preloadUsers: () => Promise<void>;
  preloadFeaturedUsers: () => Promise<void>;
  preloadAll: () => Promise<PreloadResult[]>;
  preloadCritical: () => Promise<PreloadResult[]>;
  smartPreload: (behavior: {
    visitedPortfolios: boolean;
    searchedUsers: boolean;
    viewedProfiles: string[];
  }) => Promise<void>;

  // Cache status
  isPreloading: boolean;
  lastPreloadTime: number | null;
  preloadResults: PreloadResult[];

  // Cache health monitoring
  cacheHealth: {
    isHealthy: boolean;
    issues: string[];
    recommendations: string[];
  };
}

const CacheContext = createContext<CacheContextType | undefined>(undefined);

interface CacheProviderProps {
  children: ReactNode;
}

export const CacheProvider: React.FC<CacheProviderProps> = ({ children }) => {
  const { stats, refreshStats, clearCache, preloadCache, resetStats } = useCachePerformance();
  const [isPreloading, setIsPreloading] = useState(false);
  const [lastPreloadTime, setLastPreloadTime] = useState<number | null>(null);

  // Preload users specifically
  const preloadUsers = async () => {
    setIsPreloading(true);
    try {
      await preloadCache();
      setLastPreloadTime(Date.now());
    } catch (error) {
      console.error('Failed to preload users:', error);
    } finally {
      setIsPreloading(false);
    }
  };

  // Preload featured users
  const preloadFeaturedUsers = async () => {
    setIsPreloading(true);
    try {
      console.log('Preloading featured users...');
      
      // This would typically use a cached hook, but for preloading we'll do direct fetch
      const featuredQuery = collection(db, 'users');
      const snapshot = await getDocs(featuredQuery);
      
      console.log(`Preloaded featured users data`);
      setLastPreloadTime(Date.now());
    } catch (error) {
      console.error('Failed to preload featured users:', error);
    } finally {
      setIsPreloading(false);
    }
  };

  // Analyze cache health
  const cacheHealth = React.useMemo(() => {
    const issues: string[] = [];
    const recommendations: string[] = [];
    
    // Check hit rate
    if (stats.hitRate < 50 && stats.totalHits + stats.totalMisses > 10) {
      issues.push('Low cache hit rate');
      recommendations.push('Consider increasing cache duration or preloading frequently accessed data');
    }
    
    // Check cache size
    if (stats.totalEntries > stats.maxSize * 0.8) {
      issues.push('Cache approaching maximum size');
      recommendations.push('Consider increasing MAX_CACHE_SIZE or implementing better cleanup strategies');
    }
    
    // Check for expired entries
    const expiredEntries = stats.entries.filter(entry => entry.isExpired).length;
    if (expiredEntries > stats.totalEntries * 0.3) {
      issues.push('Many expired entries in cache');
      recommendations.push('Consider running cache cleanup more frequently');
    }
    
    // Check for error entries
    const errorEntries = stats.entries.filter(entry => entry.hasError).length;
    if (errorEntries > 0) {
      issues.push(`${errorEntries} cached error entries`);
      recommendations.push('Review and clear error entries that may be preventing successful requests');
    }
    
    return {
      isHealthy: issues.length === 0,
      issues,
      recommendations
    };
  }, [stats]);

  // Auto-preload on app start
  useEffect(() => {
    const shouldPreload = !lastPreloadTime || (Date.now() - lastPreloadTime) > 30 * 60 * 1000; // 30 minutes
    
    if (shouldPreload) {
      console.log('Auto-preloading cache on app start...');
      preloadUsers();
    }
  }, []);

  // Periodic cache health monitoring
  useEffect(() => {
    const interval = setInterval(() => {
      refreshStats();
      
      // Log cache health issues
      if (!cacheHealth.isHealthy) {
        console.warn('Cache health issues detected:', cacheHealth.issues);
        console.info('Recommendations:', cacheHealth.recommendations);
      }
    }, 60000); // Check every minute

    return () => clearInterval(interval);
  }, [refreshStats, cacheHealth]);

  const value: CacheContextType = {
    stats,
    refreshStats,
    resetStats,
    clearCache,
    clearUserCache,
    clearCacheByPattern,
    preloadCache,
    preloadUsers,
    preloadFeaturedUsers,
    isPreloading,
    lastPreloadTime,
    cacheHealth
  };

  return (
    <CacheContext.Provider value={value}>
      {children}
    </CacheContext.Provider>
  );
};

// Custom hook to use cache context
export const useCache = (): CacheContextType => {
  const context = useContext(CacheContext);
  if (context === undefined) {
    throw new Error('useCache must be used within a CacheProvider');
  }
  return context;
};

// Cache debugging component (for development)
export const CacheDebugger: React.FC = () => {
  const { stats, cacheHealth, refreshStats, clearCache, preloadUsers, isPreloading } = useCache();

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-black/80 text-white p-4 rounded-lg text-xs max-w-sm">
      <div className="font-bold mb-2">Cache Debug Info</div>
      <div>Entries: {stats.totalEntries}/{stats.maxSize}</div>
      <div>Hit Rate: {stats.hitRate}%</div>
      <div>Hits: {stats.totalHits} | Misses: {stats.totalMisses}</div>
      <div className={`mt-2 ${cacheHealth.isHealthy ? 'text-green-400' : 'text-red-400'}`}>
        Health: {cacheHealth.isHealthy ? 'Good' : 'Issues'}
      </div>
      {cacheHealth.issues.length > 0 && (
        <div className="text-yellow-400 text-xs mt-1">
          {cacheHealth.issues.join(', ')}
        </div>
      )}
      <div className="mt-2 space-x-2">
        <button 
          onClick={refreshStats}
          className="bg-blue-600 px-2 py-1 rounded text-xs"
        >
          Refresh
        </button>
        <button 
          onClick={clearCache}
          className="bg-red-600 px-2 py-1 rounded text-xs"
        >
          Clear
        </button>
        <button 
          onClick={preloadUsers}
          disabled={isPreloading}
          className="bg-green-600 px-2 py-1 rounded text-xs disabled:opacity-50"
        >
          {isPreloading ? 'Loading...' : 'Preload'}
        </button>
      </div>
    </div>
  );
};

export default CacheContext;
