import { useAuth } from '@/contexts/AuthContext';
import { useEffect, useState } from 'react';
import type React from 'react';
import { getUsers, getFeaturedUsers, updateUser, deleteUser, getUserByUsername } from '@/services/firebase-db';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { Card, CardContent, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Users, Star, CheckCircle, Shield, RefreshCcw } from 'lucide-react';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import type { UserProfile } from '@/services/firebase-db';
import axios from 'axios';

interface Stat {
  label: string;
  value: number;
  icon: React.ReactNode;
  color: string; // not used for neutral look
}

interface PaymentData {
  _id: string;
  user: {
    name: string;
    email: string | null;
    photoURL: string;
    uid: string;
    createdAt: string;
  };
  phoneNumber: string;
  trxId: string;
  plan: string;
  amount: number;
  createdAt: string;
}

export default function AdminPanel() {
  const { userProfile, refreshUserProfile } = useAuth();
  const [stats, setStats] = useState<Stat[]>([]);
  const [avatarError, setAvatarError] = useState(false);
  const [loading, setLoading] = useState(true);
  const [search, setSearch] = useState('');
  const [searchResults, setSearchResults] = useState<UserProfile[]>([]);
  const [searchLoading, setSearchLoading] = useState(false);
  const [editUser, setEditUser] = useState<UserProfile | null>(null);
  const [editLoading, setEditLoading] = useState(false);
  const [editError, setEditError] = useState<string | null>(null);
  const [payments, setPayments] = useState<PaymentData[]>([]);
  const [paymentsLoading, setPaymentsLoading] = useState(false);
  const [paymentError, setPaymentError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchStats() {
      setLoading(true);
      let allUsers: any[] = [];
      let lastDoc = undefined;
      let hasMore = true;
      while (hasMore) {
        const { users, lastDoc: newLastDoc } = await getUsers(100, lastDoc);
        allUsers = allUsers.concat(users);
        if (!newLastDoc || users.length === 0) hasMore = false;
        lastDoc = newLastDoc;
      }
      const featuredUsers = await getFeaturedUsers();
      const verifiedUsers = allUsers.filter(u => u.isVerified);
      const adminUsers = allUsers.filter(u => u.isAdmin);
      setStats([
        {
          label: 'Total Users',
          value: allUsers.length,
          icon: (
            <div className="flex items-center justify-center w-14 h-14 rounded-full bg-white/30 dark:bg-white/10 backdrop-blur border border-white/30 shadow">
              <Users className="w-7 h-7 text-blue-500" strokeWidth={2.2} />
            </div>
          ),
          color: '',
        },
        {
          label: 'Featured Users',
          value: featuredUsers.length,
          icon: (
            <div className="flex items-center justify-center w-14 h-14 rounded-full bg-white/30 dark:bg-white/10 backdrop-blur border border-white/30 shadow">
              <Star className="w-7 h-7 text-yellow-400" strokeWidth={2.2} />
            </div>
          ),
          color: '',
        },
        {
          label: 'Verified Users',
          value: verifiedUsers.length,
          icon: (
            <div className="flex items-center justify-center w-14 h-14 rounded-full bg-white/30 dark:bg-white/10 backdrop-blur border border-white/30 shadow">
              <CheckCircle className="w-7 h-7 text-green-500" strokeWidth={2.2} />
            </div>
          ),
          color: '',
        },
        {
          label: 'Admins',
          value: adminUsers.length,
          icon: (
            <div className="flex items-center justify-center w-14 h-14 rounded-full bg-white/30 dark:bg-white/10 backdrop-blur border border-white/30 shadow">
              <Shield className="w-7 h-7 text-purple-500" strokeWidth={2.2} />
            </div>
          ),
          color: '',
        },
      ]);
      setLoading(false);
    }
    fetchStats();
  }, []);

  // Search users by displayName or username
  const handleSearch = async () => {
    setSearchLoading(true);
    setSearchResults([]);
    let results: UserProfile[] = [];
    // Try username exact match first
    const byUsername = await getUserByUsername(search);
    if (byUsername) results.push(byUsername);
    // Then search all users for displayName match (case-insensitive substring)
    let allUsers: UserProfile[] = [];
    let lastDoc = undefined;
    let hasMore = true;
    while (hasMore) {
      const { users, lastDoc: newLastDoc } = await getUsers(100, lastDoc);
      allUsers = allUsers.concat(users);
      if (!newLastDoc || users.length === 0) hasMore = false;
      lastDoc = newLastDoc;
    }
    const byDisplayName = allUsers.filter(u => u.displayName.toLowerCase().includes(search.toLowerCase()));
    // Avoid duplicates
    results = results.concat(byDisplayName.filter(u => !results.some(r => r.uid === u.uid)));
    setSearchResults(results);
    setSearchLoading(false);
  };

  // Handle edit user open
  const openEditUser = (user: UserProfile) => {
    setEditUser(user);
    setEditError(null);
  };

  // Handle edit user save
  const handleEditSave = async () => {
    if (!editUser) return;
    setEditLoading(true);
    setEditError(null);
    const { uid, ...data } = editUser;
    // Ensure createdAt is always present
    if (!('createdAt' in data) && searchResults.length > 0) {
      const original = searchResults.find(u => u.uid === uid);
      if (original && original.createdAt) {
        (data as any).createdAt = original.createdAt;
      }
    }
    const success = await updateUser(uid, data);
    if (success) {
      setEditUser(null);
      handleSearch(); // Refresh search results
    } else {
      setEditError('Failed to update user.');
    }
    setEditLoading(false);
  };

  // Handle delete user
  const handleDeleteUser = async (uid: string) => {
    if (!window.confirm('Are you sure you want to delete this user?')) return;
    await deleteUser(uid);
    handleSearch();
  };

  // Handle edit user field change
  const handleEditChange = (field: keyof UserProfile, value: any) => {
    if (!editUser) return;
    setEditUser({ ...editUser, [field]: value });
  };

  // Fetch payments data
  const fetchPayments = async () => {
    try {
      setPaymentsLoading(true);
      setPaymentError(null);
      const response = await axios.get('https://api.seraprogrammer.com/api/payment/');
      setPayments(response.data);
    } catch (error) {
      setPaymentError('Failed to fetch payments');
      console.error('Error fetching payments:', error);
    } finally {
      setPaymentsLoading(false);
    }
  };

  // Confirm payment
  const handleConfirmPayment = async (id: string) => {
    try {
      setPaymentError(null);
      const payment = payments.find(p => p._id === id);
      if (!payment) {
        setPaymentError('Payment not found');
        return;
      }

      // Get current user data to check status
      const userData = await getUserByUsername(payment.user.uid);
      if (userData && userData.isVerified && userData.isFeatured) {
        setPaymentError('User is already verified and featured');
        return;
      }

      // First confirm the payment
      await axios.post(`https://api.seraprogrammer.com/api/payment/confirm/${id}`);

      // Then update user status in Firebase
      const updateData = {
        isVerified: true,
        isFeatured: true
      };

      const success = await updateUser(payment.user.uid, updateData);
      if (!success) {
        setPaymentError('Payment confirmed but failed to update user status');
        return;
      }

      // Refresh both payments and user data
      fetchPayments();
      handleSearch(); // Refresh user list if it's open
    } catch (error) {
      setPaymentError('Failed to confirm payment and update user status');
      console.error('Error confirming payment:', error);
    }
  };

  // Delete payment
  const handleDeletePayment = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this payment?')) return;
    try {
      setPaymentError(null);
      await axios.delete(`https://api.seraprogrammer.com/api/payment/${id}`);
      fetchPayments(); // Refresh payments list
    } catch (error) {
      setPaymentError('Failed to delete payment');
      console.error('Error deleting payment:', error);
    }
  };

  useEffect(() => {
    fetchPayments();
  }, []);

  return (
    <div className="min-h-screen bg-background text-foreground flex flex-col items-center py-0 md:py-8">
      {/* Subtle Animated Gradient Header */}
      <div className="w-full flex justify-center">
        <div className="relative w-full max-w-7xl overflow-hidden rounded-b-2xl shadow-sm mb-12">
          <div className="absolute inset-0 bg-gradient-to-r from-zinc-900 via-zinc-800 to-zinc-900 animate-gradient-move opacity-70" style={{zIndex:1}} />
          <div className="relative z-10 flex flex-col md:flex-row items-center justify-between px-6 py-8 md:px-12">
            <h1 className="text-3xl md:text-4xl font-bold tracking-tight mb-4 md:mb-0">Admin Dashboard</h1>
            {loading ? (
              <div className="flex items-center gap-4">
                <Skeleton className="h-10 w-32 rounded-full" />
                <Skeleton className="h-12 w-12 rounded-full" />
              </div>
            ) : userProfile && (
              <div className="flex items-center gap-4">
                <Button
                  onClick={refreshUserProfile}
                  variant="outline"
                  className="border-zinc-700 text-zinc-700 dark:text-white bg-white/10 hover:bg-white/20 shadow-sm"
                  size="icon"
                  title="Refresh"
                >
                    <RefreshCcw className="w-7 h-7 text-blue-500" strokeWidth={2.2} />
                  </Button>
                <span className="font-semibold text-lg md:text-xl drop-shadow-sm">{userProfile.displayName}</span>
                <Avatar className="h-12 w-12 border border-zinc-300 dark:border-zinc-700 shadow-sm">
                  <AvatarImage
                    src={avatarError ? undefined : userProfile.photoURL || undefined}
                    alt={userProfile.displayName || 'Admin Avatar'}
                    onError={() => setAvatarError(true)}
                  />
                  <AvatarFallback>
                    {userProfile.displayName?.split(' ').map(n => n[0]).join('').toUpperCase()}
                  </AvatarFallback>
                </Avatar>
              </div>
            )}
          </div>
        </div>
      </div>
      {/* Stats Cards */}
      <div className="w-full max-w-7xl grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6 px-4 md:px-0">
        {loading
          ? Array.from({ length: 4 }).map((_, i) => (
              <Skeleton key={i} className="h-40 w-full rounded-xl" />
            ))
          : stats.map(stat => (
              <Card key={stat.label} className="bg-card border border-zinc-200 dark:border-zinc-800 shadow-sm rounded-xl flex flex-col items-center justify-center transition-transform duration-200 hover:scale-[1.02]">
                <CardContent className="flex flex-col items-center justify-center h-40 gap-3">
                  {stat.icon}
                  <CardTitle className="text-2xl font-bold">{stat.value}</CardTitle>
                  <span className="text-base font-medium text-zinc-500 dark:text-zinc-400">{stat.label}</span>
                </CardContent>
              </Card>
            ))}
      </div>
      {/* Payments Section */}
      <div className="w-full max-w-7xl mt-12 px-4 md:px-0">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold">Payment Management</h2>
          <Button onClick={fetchPayments} disabled={paymentsLoading}>
            <RefreshCcw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
        </div>
        
        {paymentError && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {paymentError}
          </div>
        )}

        {paymentsLoading ? (
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, i) => (
              <Skeleton key={i} className="h-24 w-full rounded-xl" />
            ))}
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full border text-sm">
              <thead>
                <tr className="bg-zinc-100 dark:bg-zinc-800">
                  <th className="p-2 border">User</th>
                  <th className="p-2 border">Phone</th>
                  <th className="p-2 border">Transaction ID</th>
                  <th className="p-2 border">Plan</th>
                  <th className="p-2 border">Amount</th>
                  <th className="p-2 border">Date</th>
                  <th className="p-2 border">Actions</th>
                </tr>
              </thead>
              <tbody>
                {payments.map(payment => (
                  <tr key={payment._id}>
                    <td className="p-2 border">
                      <div className="flex items-center gap-2">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={payment.user.photoURL} alt={payment.user.name} />
                          <AvatarFallback>
                            {payment.user.name?.split(' ').map(n => n[0]).join('').toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">{payment.user.name}</div>
                          <div className="text-xs text-zinc-500">{payment.user.email || 'No email'}</div>
                        </div>
                      </div>
                    </td>
                    <td className="p-2 border">{payment.phoneNumber}</td>
                    <td className="p-2 border">{payment.trxId}</td>
                    <td className="p-2 border">
                      <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
                        {payment.plan}
                      </span>
                    </td>
                    <td className="p-2 border">${payment.amount}</td>
                    <td className="p-2 border">{new Date(payment.createdAt).toLocaleDateString()}</td>
                    <td className="p-2 border">
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          onClick={() => handleConfirmPayment(payment._id)}
                          className="bg-green-500 hover:bg-green-600"
                        >
                          Confirm
                        </Button>
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => handleDeletePayment(payment._id)}
                        >
                          Delete
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
      {/* User Search & Management */}
      <div className="w-full max-w-7xl mt-12 px-4 md:px-0">
        <h2 className="text-xl font-bold mb-4">User Management</h2>
        <div className="flex gap-2 mb-4">
          <Input
            placeholder="Search by username or display name"
            value={search}
            onChange={e => setSearch(e.target.value)}
            onKeyDown={e => { if (e.key === 'Enter') handleSearch(); }}
            className="w-64"
          />
          <Button onClick={handleSearch} disabled={searchLoading || !search}>
            {searchLoading ? 'Searching...' : 'Search'}
          </Button>
        </div>
        {searchResults.length > 0 && (
          <div className="overflow-x-auto">
            <table className="min-w-full border text-sm">
              <thead>
                <tr className="bg-zinc-100 dark:bg-zinc-800">
                  <th className="p-2 border">Photo</th>
                  <th className="p-2 border">UID</th>
                  <th className="p-2 border">Display Name</th>
                  <th className="p-2 border">Username</th>
                  <th className="p-2 border">Email</th>
                  <th className="p-2 border">Verified</th>
                  <th className="p-2 border">Featured</th>
                  <th className="p-2 border">Admin</th>
                  <th className="p-2 border">Actions</th>
                </tr>
              </thead>
              <tbody>
                {searchResults.map(user => (
                  <tr key={user.uid}>
                    <td className="p-2 border">
                      <Avatar className="h-10 w-10 border border-zinc-300 dark:border-zinc-700 shadow-sm">
                        <AvatarImage src={user.photoURL || undefined} alt={user.displayName || 'User Avatar'} />
                        <AvatarFallback>
                          {user.displayName?.split(' ').map(n => n[0]).join('').toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                    </td>
                    <td className="p-2 border">{user.uid}</td>
                    <td className="p-2 border">{user.displayName}</td>
                    <td className="p-2 border">{user.username}</td>
                    <td className="p-2 border">{user.email}</td>
                    <td className="p-2 border">{user.isVerified ? 'Yes' : 'No'}</td>
                    <td className="p-2 border">{user.isFeatured ? 'Yes' : 'No'}</td>
                    <td className="p-2 border">{user.isAdmin ? 'Yes' : 'No'}</td>
                    <td className="p-2 border flex gap-2">
                      <Button size="sm" variant="outline" onClick={() => openEditUser(user)}>Edit</Button>
                      <Button size="sm" variant="destructive" onClick={() => handleDeleteUser(user.uid)}>Delete</Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
      {/* Edit User Dialog */}
      <Dialog open={!!editUser} onOpenChange={open => !open && setEditUser(null)}>
        <DialogContent>
          {editUser && (
            <div className="space-y-4 max-h-[70vh] overflow-y-auto pr-2">
              <h3 className="text-lg font-bold mb-2">Edit User</h3>
              <div className="flex flex-col gap-2">
                <label>Display Name
                  <Input value={editUser.displayName} onChange={e => handleEditChange('displayName', e.target.value)} />
                </label>
                <label>Username
                  <Input value={editUser.username} onChange={e => handleEditChange('username', e.target.value)} />
                </label>
                <label>Email
                  <Input value={editUser.email || ''} onChange={e => handleEditChange('email', e.target.value)} />
                </label>
                <label>Bio
                  <textarea className="border rounded p-2 w-full" value={editUser.bio} onChange={e => handleEditChange('bio', e.target.value)} />
                </label>
                <label>Country
                  <Input value={editUser.country} onChange={e => handleEditChange('country', e.target.value)} />
                </label>
                <label>Role
                  <Input value={editUser.role} onChange={e => handleEditChange('role', e.target.value)} />
                </label>
                <label>Seniority Level
                  <Input value={editUser.seniorityLevel} onChange={e => handleEditChange('seniorityLevel', e.target.value)} />
                </label>
                <label>Website
                  <Input value={editUser.website} onChange={e => handleEditChange('website', e.target.value)} />
                </label>
                <label>GitHub URL
                  <Input value={editUser.githubUrl} onChange={e => handleEditChange('githubUrl', e.target.value)} />
                </label>
                <label>Twitter URL
                  <Input value={editUser.twitterUrl} onChange={e => handleEditChange('twitterUrl', e.target.value)} />
                </label>
                <label>LinkedIn URL
                  <Input value={editUser.linkedinUrl} onChange={e => handleEditChange('linkedinUrl', e.target.value)} />
                </label>
                <label>Tech Stack (comma separated)
                  <Input value={editUser.techStack?.join(', ') || ''} onChange={e => handleEditChange('techStack', e.target.value.split(',').map(s => s.trim()))} />
                </label>
                <label>Banner URL
                  <Input value={editUser.bannerURL} onChange={e => handleEditChange('bannerURL', e.target.value)} />
                </label>
                <label>Photo URL
                  <Input value={editUser.photoURL || ''} onChange={e => handleEditChange('photoURL', e.target.value)} />
                </label>
                <label>Available For Work
                  <Switch checked={!!editUser.availableForWork} onCheckedChange={v => handleEditChange('availableForWork', v)} />
                </label>
                <label>Work Type
                  <Input value={editUser.workType || ''} onChange={e => handleEditChange('workType', e.target.value)} />
                </label>
                <label>Hourly Rate
                  <Input value={editUser.hourlyRate || ''} onChange={e => handleEditChange('hourlyRate', e.target.value)} />
                </label>
                <label>Verified
                  <Switch checked={editUser.isVerified} onCheckedChange={v => handleEditChange('isVerified', v)} />
                </label>
                <label>Featured
                  <Switch checked={editUser.isFeatured} onCheckedChange={v => handleEditChange('isFeatured', v)} />
                </label>
                <label>Admin
                  <Switch checked={editUser.isAdmin} onCheckedChange={v => handleEditChange('isAdmin', v)} />
                </label>
                <label>About (ascript)
                  <textarea className="border rounded p-2 w-full" value={editUser.ascript || ''} onChange={e => handleEditChange('ascript', e.target.value)} />
                </label>
              </div>
              {editError && <div className="text-red-500">{editError}</div>}
              <div className="flex gap-2 justify-end">
                <Button onClick={() => setEditUser(null)} variant="outline">Cancel</Button>
                <Button onClick={handleEditSave} disabled={editLoading}>
                  {editLoading ? 'Saving...' : 'Save'}
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
      {/* Gradient animation keyframes */}
      <style>{`
        @keyframes gradient-move {
          0% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
          100% { background-position: 0% 50%; }
        }
        .animate-gradient-move {
          background-size: 200% 200%;
          animation: gradient-move 8s ease-in-out infinite;
        }
      `}</style>
    </div>
  );
}
