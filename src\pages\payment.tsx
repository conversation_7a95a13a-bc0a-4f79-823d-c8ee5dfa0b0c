import { useSearchParams } from 'react-router-dom';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useAuth } from "@/contexts/AuthContext";
import { useState } from "react";

export default function Payment() {
  const [searchParams] = useSearchParams();
  const plan = searchParams.get('plan');
  const { user } = useAuth();
  const [paymentData, setPaymentData] = useState({
    phoneNumber: '',
    trxId: ''
  });

  // Find plan details from the plans array (you can move this to a shared config)
  const plans = {
    'free': { name: 'Free', price: 0, takaPrice: 0 },
    '6-months': { name: 'Pro', price: 1.64, takaPrice: 200 },
    'yearly': { name: 'Business', price: 2.86, takaPrice: 350 }
  };

  const currentPlan = plans[plan as keyof typeof plans] || plans['free'];

  const handlePaymentSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const response = await fetch('https://api.seraprogrammer.com/api/payment/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          phoneNumber: paymentData.phoneNumber,
          trxId: paymentData.trxId,
          plan: currentPlan.name,
          amount: currentPlan.takaPrice,
          user: {
            name: user?.displayName,
            email: user?.email,
            photoURL: user?.photoURL,
            uid: user?.uid,
            createdAt: user?.metadata?.creationTime
          }
        }),
      });
      if (response.ok) {
        alert('Payment submitted successfully!');
      } else {
        alert('Payment submission failed. Please try again.');
      }
    } catch (error) {
      console.error('Payment submission error:', error);
      alert('Payment submission failed. Please try again.');
    }
  };

  return (
    <div className="min-h-screen bg-background p-8">
      <div className="max-w-2xl mx-auto bg-card text-card-foreground rounded-xl shadow-lg p-6 border border-border">
        <h1 className="text-3xl font-bold mb-6">Complete Your Payment</h1>
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-2">Selected Plan: {currentPlan.name}</h2>
          <p className="text-muted-foreground">Please choose your payment location to proceed</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline" className="w-full p-4">
                Pay in Bangladesh
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-3xl">
              <DialogHeader>
                <DialogTitle>bKash Manual Payment</DialogTitle>
              </DialogHeader>
              <div className="p-4">
                <div className="flex items-center gap-4 mb-6">
                  <img src="https://i.postimg.cc/7hcrv8Nn/62f92578037f0.png" alt="bKash" className="w-16 h-16 object-contain" />
                  <div>
                    <h3 className="font-semibold">bKash Manual Payment</h3>
                    <p className="text-muted-foreground">Follow the instructions below</p>
                  </div>
                </div>

                <form onSubmit={handlePaymentSubmit} className="space-y-6 mb-8">
                  <div className="space-y-4">
                    <Input
                      placeholder="Your bKash Number"
                      value={paymentData.phoneNumber}
                      onChange={(e) => setPaymentData(prev => ({ ...prev, phoneNumber: e.target.value }))}
                      required
                    />
                    <Input
                      placeholder="Transaction ID (TrxID)"
                      value={paymentData.trxId}
                      onChange={(e) => setPaymentData(prev => ({ ...prev, trxId: e.target.value }))}
                      required
                    />
                    <Button type="submit" className="w-full">
                      Submit Payment
                    </Button>
                  </div>
                </form>

                <div className="space-y-4 text-sm">
                  <p className="font-semibold">বিকাশ নাম্বারঃ ০১৪০১৮০৫২৭৭ (পার্সোনাল একাউন্ট)</p>
                  <div>
                    <p className="font-semibold mb-2">পেমেন্ট করার নিয়মঃ</p>
                    <ol className="list-decimal pl-5 space-y-2">
                      <li>*247# ডায়াল করো বা বিকাশ মোবাইল অ্যাপ-এ যাও।</li>
                      <li>পেমেন্ট অপশন সিলেক্ট করো।</li>
                      <li>আমাদের বিকাশ পার্সোনাল নাম্বারঃ ০১৪০১৮০৫২৭৭ লিখো।</li>
                      <li>টাকার পরিমানঃ ৳{currentPlan.takaPrice}</li>
                      <li>রেফারেন্সঃ তোমার নাম</li>
                      <li>কাউন্টার নাম্বারঃ ১</li>
                      <li>তোমার পিন নাম্বার দিয়ে পেমেন্ট কমপ্লিট করো</li>
                    </ol>
                  </div>
                  <div className="bg-muted/50 p-4 rounded-lg mt-4">
                    <p className="font-medium">ট্রানজেকশন আইডি/TrxID দেওয়ার সময় বিশেষ নির্দেশনাঃ</p>
                    <p className="mt-2">অবশ্যই ভাল করে খেয়াল করে, জিরো 'O', ইংরেজি ও '০', ইংরেজি বড় হাতে অক্ষর আই 'I' ও ইংরেজি ছোট হাতে অক্ষর এল 'l' দেখে দিবে। সাধারন ভাবে ট্রানজেকশন আইডি/TrxID বড় হাতে অক্ষরে দেওয়া থাকে।</p>
                  </div>
                </div>
              </div>
            </DialogContent>
          </Dialog>

          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline" className="w-full p-4">
                International Payment
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-3xl">
              <DialogHeader>
                <DialogTitle>International Payment - TapTap Send</DialogTitle>
              </DialogHeader>
              <div className="p-4">
                <div className="flex items-center gap-4 mb-6">
                  <img 
                    src="https://cdn.prod.website-files.com/5ae897b18423ad8b62ceba7c/5b3a97163c43bae706308fca_logo_horz_mint_bg%402x.png" 
                    alt="TapTap Send" 
                    className="h-12 object-contain"
                  />
                </div>

                <div className="space-y-6">
                  <div className="bg-muted/30 p-4 rounded-lg">
                    <p className="font-medium mb-2">Important Note:</p>
                    <p>Please check if TapTap Send is supported in your country before proceeding. TapTap Send offers low-cost international money transfers to Bangladesh.</p>
                  </div>

                  <div>
                    <h3 className="font-semibold mb-3">How to Pay using TapTap Send:</h3>
                    <ol className="list-decimal pl-5 space-y-3">
                      <li>Download TapTap Send from your app store</li>
                      <li>Create an account and verify your identity</li>
                      <li>Select Bangladesh as the destination country</li>
                      <li>Enter the recipient number: <span className="font-mono bg-muted/30 px-2 py-1 rounded">***********</span></li>
                      <li>Enter the amount: ${currentPlan.price}</li>
                      <li>Complete the payment using your preferred method in the app</li>
                    </ol>
                  </div>

                  <div className="bg-primary/10 p-4 rounded-lg">
                    <p className="font-medium">Currently Supported Payment Method:</p>
                    <p className="mt-2">We currently only support TapTap Send for international payments due to its low fees and fast transfer times to Bangladesh.</p>
                  </div>

                  <Button 
                    variant="outline" 
                    className="w-full"
                    onClick={() => window.open('https://taptapsend.com', '_blank')}
                  >
                    Go to TapTap Send Website
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>

        <div className="mt-8 pt-8 border-t border-border">
          <h2 className="text-xl font-semibold mb-4">Need Help with Payment?</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="space-y-4">
              <h3 className="font-medium">Contact via WhatsApp</h3>
              <Dialog>
                <DialogTrigger asChild>
                  <div className="flex justify-center bg-white p-4 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                    <img 
                      src="https://i.postimg.cc/0j093XKs/Screenshot-2025-06-20-032958.png" 
                      alt="WhatsApp QR Code" 
                      className="w-48 h-48 object-contain"
                    />
                  </div>
                </DialogTrigger>
                <DialogContent className="max-w-2xl">
                  <DialogHeader>
                    <DialogTitle>Scan QR Code to Contact on WhatsApp</DialogTitle>
                  </DialogHeader>
                  <div className="flex justify-center p-8">
                    <img 
                      src="https://i.postimg.cc/0j093XKs/Screenshot-2025-06-20-032958.png" 
                      alt="WhatsApp QR Code Large" 
                      className="w-96 h-96 object-contain"
                    />
                  </div>
                </DialogContent>
              </Dialog>
              <p className="text-sm text-muted-foreground text-center">
                Click on the QR code to enlarge for scanning
              </p>
            </div>

            <div className="space-y-4">
              <h3 className="font-medium">Contact via Email</h3>
              <div className="bg-muted/30 p-6 rounded-lg flex flex-col items-center justify-center h-full">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mb-4 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
                <a 
                  href="mailto:<EMAIL>" 
                  className="text-primary hover:underline font-medium"
                >
                  <EMAIL>
                </a>
                <p className="text-sm text-muted-foreground text-center mt-2">
                  Send us an email for any payment-related queries
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
