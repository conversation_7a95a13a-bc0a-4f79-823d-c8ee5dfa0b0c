import { collection, getDocs, query, where, orderBy, limit } from 'firebase/firestore';
import { db } from '@/config/firebase';
import type { UserProfile } from '@/services/firebase-db';

interface PreloadStrategy {
  name: string;
  priority: number;
  execute: () => Promise<void>;
  condition?: () => boolean;
}

interface PreloadResult {
  strategy: string;
  success: boolean;
  duration: number;
  dataSize: number;
  error?: string;
}

class CachePreloader {
  private strategies: PreloadStrategy[] = [];
  private isPreloading = false;
  private preloadResults: PreloadResult[] = [];

  constructor() {
    this.initializeStrategies();
  }

  private initializeStrategies() {
    // Strategy 1: Preload all users (highest priority)
    this.strategies.push({
      name: 'all-users',
      priority: 1,
      execute: this.preloadAllUsers.bind(this),
      condition: () => !this.isCached('all-users')
    });

    // Strategy 2: Preload featured users
    this.strategies.push({
      name: 'featured-users',
      priority: 2,
      execute: this.preloadFeaturedUsers.bind(this),
      condition: () => !this.isCached('featured-users')
    });

    // Strategy 3: Preload verified users
    this.strategies.push({
      name: 'verified-users',
      priority: 3,
      execute: this.preloadVerifiedUsers.bind(this),
      condition: () => !this.isCached('verified-users')
    });

    // Strategy 4: Preload new users
    this.strategies.push({
      name: 'new-users',
      priority: 4,
      execute: this.preloadNewUsers.bind(this),
      condition: () => !this.isCached('new-users')
    });

    // Sort strategies by priority
    this.strategies.sort((a, b) => a.priority - b.priority);
  }

  private isCached(key: string): boolean {
    try {
      const cached = localStorage.getItem(`firebase_cache_${key}`);
      if (!cached) return false;

      const data = JSON.parse(cached);
      const now = Date.now();
      const maxAge = key === 'all-users' ? 10 * 60 * 1000 : 5 * 60 * 1000; // 10 min for all users, 5 min for others

      return (now - data.timestamp) < maxAge;
    } catch {
      return false;
    }
  }

  private async preloadAllUsers(): Promise<void> {
    console.log('Preloading all users...');
    const usersRef = collection(db, 'users');
    const snapshot = await getDocs(usersRef);
    
    const users: UserProfile[] = snapshot.docs.map(doc => ({
      uid: doc.id,
      ...doc.data()
    } as UserProfile));

    this.cacheData('all-users', users);
    console.log(`Preloaded ${users.length} users`);
  }

  private async preloadFeaturedUsers(): Promise<void> {
    console.log('Preloading featured users...');
    const featuredQuery = query(
      collection(db, 'users'),
      where('isFeatured', '==', true),
      orderBy('displayName')
    );

    const snapshot = await getDocs(featuredQuery);
    const users: UserProfile[] = snapshot.docs.map(doc => ({
      uid: doc.id,
      ...doc.data()
    } as UserProfile));

    this.cacheData('featured-users', users);
    console.log(`Preloaded ${users.length} featured users`);
  }

  private async preloadVerifiedUsers(): Promise<void> {
    console.log('Preloading verified users...');
    const verifiedQuery = query(
      collection(db, 'users'),
      where('isVerified', '==', true),
      orderBy('displayName'),
      limit(20) // Limit to prevent excessive data
    );

    const snapshot = await getDocs(verifiedQuery);
    const users: UserProfile[] = snapshot.docs.map(doc => ({
      uid: doc.id,
      ...doc.data()
    } as UserProfile));

    this.cacheData('verified-users', users);
    console.log(`Preloaded ${users.length} verified users`);
  }

  private async preloadNewUsers(): Promise<void> {
    console.log('Preloading new users...');
    const now = Date.now();
    const twentyFourHoursAgo = now - (24 * 60 * 60 * 1000);

    const newUsersQuery = query(
      collection(db, 'users'),
      where('createdAt', '>=', twentyFourHoursAgo),
      orderBy('createdAt', 'desc'),
      limit(10) // Limit to recent new users
    );

    const snapshot = await getDocs(newUsersQuery);
    const users: UserProfile[] = snapshot.docs.map(doc => ({
      uid: doc.id,
      ...doc.data()
    } as UserProfile));

    this.cacheData('new-users', users);
    console.log(`Preloaded ${users.length} new users`);
  }

  private cacheData(key: string, data: any): void {
    try {
      const cacheEntry = {
        version: '1.0',
        key,
        data,
        timestamp: Date.now(),
        error: null,
        hitCount: 0,
        lastAccessed: Date.now()
      };

      localStorage.setItem(`firebase_cache_${key}`, JSON.stringify(cacheEntry));
    } catch (error) {
      console.warn(`Failed to cache ${key}:`, error);
    }
  }

  async preloadAll(): Promise<PreloadResult[]> {
    if (this.isPreloading) {
      console.log('Preloading already in progress...');
      return this.preloadResults;
    }

    this.isPreloading = true;
    this.preloadResults = [];

    console.log('Starting cache preloading...');

    for (const strategy of this.strategies) {
      if (strategy.condition && !strategy.condition()) {
        console.log(`Skipping ${strategy.name} - condition not met`);
        continue;
      }

      const startTime = Date.now();
      let result: PreloadResult;

      try {
        await strategy.execute();
        const duration = Date.now() - startTime;
        
        result = {
          strategy: strategy.name,
          success: true,
          duration,
          dataSize: this.getDataSize(strategy.name)
        };

        console.log(`✅ ${strategy.name} preloaded in ${duration}ms`);
      } catch (error) {
        const duration = Date.now() - startTime;
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        
        result = {
          strategy: strategy.name,
          success: false,
          duration,
          dataSize: 0,
          error: errorMessage
        };

        console.error(`❌ ${strategy.name} failed:`, error);
      }

      this.preloadResults.push(result);
    }

    this.isPreloading = false;
    
    const totalDuration = this.preloadResults.reduce((sum, result) => sum + result.duration, 0);
    const successCount = this.preloadResults.filter(result => result.success).length;
    
    console.log(`Cache preloading completed: ${successCount}/${this.preloadResults.length} strategies succeeded in ${totalDuration}ms`);
    
    return this.preloadResults;
  }

  async preloadCritical(): Promise<PreloadResult[]> {
    // Only preload the most critical data (all users)
    const criticalStrategies = this.strategies.filter(s => s.priority <= 1);
    
    this.isPreloading = true;
    this.preloadResults = [];

    for (const strategy of criticalStrategies) {
      if (strategy.condition && !strategy.condition()) continue;

      const startTime = Date.now();
      try {
        await strategy.execute();
        this.preloadResults.push({
          strategy: strategy.name,
          success: true,
          duration: Date.now() - startTime,
          dataSize: this.getDataSize(strategy.name)
        });
      } catch (error) {
        this.preloadResults.push({
          strategy: strategy.name,
          success: false,
          duration: Date.now() - startTime,
          dataSize: 0,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    this.isPreloading = false;
    return this.preloadResults;
  }

  private getDataSize(key: string): number {
    try {
      const cached = localStorage.getItem(`firebase_cache_${key}`);
      return cached ? cached.length : 0;
    } catch {
      return 0;
    }
  }

  getPreloadResults(): PreloadResult[] {
    return [...this.preloadResults];
  }

  isCurrentlyPreloading(): boolean {
    return this.isPreloading;
  }

  // Smart preloading based on user behavior
  async smartPreload(userBehavior: {
    visitedPortfolios: boolean;
    searchedUsers: boolean;
    viewedProfiles: string[];
  }): Promise<void> {
    console.log('Starting smart preload based on user behavior...');

    // If user visited portfolios, prioritize all users
    if (userBehavior.visitedPortfolios && !this.isCached('all-users')) {
      await this.preloadAllUsers();
    }

    // If user searched, preload featured and verified users
    if (userBehavior.searchedUsers) {
      if (!this.isCached('featured-users')) {
        await this.preloadFeaturedUsers();
      }
      if (!this.isCached('verified-users')) {
        await this.preloadVerifiedUsers();
      }
    }

    // Preload related users based on viewed profiles
    if (userBehavior.viewedProfiles.length > 0) {
      // This could be enhanced to preload users with similar tech stacks, etc.
      console.log(`User viewed ${userBehavior.viewedProfiles.length} profiles`);
    }
  }
}

// Export singleton instance
export const cachePreloader = new CachePreloader();

// Export types
export type { PreloadResult, PreloadStrategy };
