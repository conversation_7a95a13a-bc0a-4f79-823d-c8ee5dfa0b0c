import React, { useState, useCallback, useEffect, useMemo } from "react";
import { But<PERSON> } from "../components/ui/button";
import { MagicCard } from "../components/magicui/magic-card";
import { Skeleton } from "../components/ui/skeleton";
import {
  AlertCircle,
  Users,
  ExternalLink,
  Star,
  Code2,
  Cpu,
  Brain,
  Users2,
  Lightbulb,
  Building2,
  Crown,
  CheckCircle,
  RefreshCw,
  Loader2,
} from "lucide-react";
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider,
} from "../components/ui/tooltip";
import { useNavigate } from "react-router-dom";
import {  QueryDocumentSnapshot } from 'firebase/firestore';
import type { DocumentData } from 'firebase/firestore';
import { countries as allCountries } from "../components/ui/country-select";
import { useAllUsers } from '../hooks/useFirebaseCache';
import { useGenerateSEO, useSEOFromMetadata } from '../hooks/useSEO';

interface User {
  uid: string;
  displayName: string;
  username: string;
  email: string | null;
  isFeatured: boolean;
  isVerified: boolean;
  photoURL?: string | null;
  bannerURL?: string;
  bio?: string;
  role?: string;
  country?: string;
  githubUrl?: string;
  linkedinUrl?: string;
  availableForWork?: boolean;
  seniorityLevel?: string;
  techStack?: string[];
  createdAt?: number;
  updatedAt?: number;
}

interface CachedData {
  users: User[];
  remainingUsers: User[];
  lastFetch: number;
  hasMore: boolean;
  lastDoc: any;
}

type SeniorityLevel =
  | "Junior"
  | "Mid-Level"
  | "Senior"
  | "Lead"
  | "Principal"
  | "Architect"
  | "CTO";

type SeniorityIcon = {
  icon: React.ForwardRefExoticComponent<any>;
  color: string;
};

const seniorityIcons: Record<SeniorityLevel, SeniorityIcon> = {
  Junior: { icon: Code2, color: "text-blue-500" },
  "Mid-Level": { icon: Cpu, color: "text-green-500" },
  Senior: { icon: Brain, color: "text-purple-500" },
  Lead: { icon: Users2, color: "text-amber-500" },
  Principal: { icon: Lightbulb, color: "text-red-500" },
  Architect: { icon: Building2, color: "text-indigo-500" },
  CTO: { icon: Crown, color: "text-yellow-500" },
};

const isSeniorityLevel = (level: string): level is SeniorityLevel => {
  return level in seniorityIcons;
};

const isNewProfile = (createdAt: number | undefined, updatedAt: number | undefined) => {
  if (!createdAt) return false;

  const now = Date.now();
  const twentyFourHours = 24 * 60 * 60 * 1000;
  const oneHour = 60 * 60 * 1000;

  const isRecentlyCreated = now - createdAt < twentyFourHours;

  if (!updatedAt) {
    return isRecentlyCreated;
  }

  const timeBetweenCreateAndUpdate = updatedAt - createdAt;
  const isTrulyNew = isRecentlyCreated && timeBetweenCreateAndUpdate < oneHour;

  return isTrulyNew;
};

const getCountryInfo = (countryCodeOrName: string | undefined) => {
  if (!countryCodeOrName) return null;

  // Try to match by code (case-insensitive)
  let country = allCountries.find(
    (country) => country.code.toLowerCase() === countryCodeOrName.toLowerCase()
  );

  // If not found, try to match by name (case-insensitive)
  if (!country) {
    country = allCountries.find(
      (country) => country.name.toLowerCase() === countryCodeOrName.toLowerCase()
    );
  }

  return country;
};

// Cache utilities
const CACHE_KEY = 'portfolios_cache';
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

const getCachedData = (): CachedData | null => {
  try {
    const cached = localStorage.getItem(CACHE_KEY);
    if (!cached) return null;
    
    const data: CachedData = JSON.parse(cached);
    const now = Date.now();
    
    // Check if cache is still valid
    if (now - data.lastFetch > CACHE_DURATION) {
      localStorage.removeItem(CACHE_KEY);
      return null;
    }
    
    return data;
  } catch (error) {
    console.error('Error reading cache:', error);
    return null;
  }
};

const setCachedData = (data: CachedData) => {
  try {
    localStorage.setItem(CACHE_KEY, JSON.stringify(data));
  } catch (error) {
    console.error('Error writing cache:', error);
  }
};

export default function Portfolios() {
  const navigate = useNavigate();
  const [displayedUsers, setDisplayedUsers] = useState<User[]>([]);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [currentPage, setCurrentPage] = useState(0);
  const [remainingUsers, setRemainingUsers] = useState<User[]>([]);

  const ITEMS_PER_LOAD = 32;

  // Use cached hook for all users
  const { data: allUsers, isLoading, error: fetchError, fromCache } = useAllUsers();
  const error = fetchError;

  // SEO hooks
  const { generatePortfoliosSEO } = useGenerateSEO();

  // Process users when allUsers data changes
  const processUsers = useCallback(() => {
    if (!allUsers || allUsers.length === 0) return;

    console.log(`Processing ${allUsers.length} users from cache...`);

    // Cast UserProfile to User type and prioritize users
    const users = allUsers as User[];

    const featuredUsers = users.filter(user => user.isFeatured);
    const verifiedUsers = users.filter(user => user.isVerified && !user.isFeatured);
    const newUsers = users.filter(user =>
      !user.isFeatured &&
      !user.isVerified &&
      isNewProfile(user.createdAt, user.updatedAt)
    );
    const normalUsers = users.filter(user =>
      !user.isFeatured &&
      !user.isVerified &&
      !isNewProfile(user.createdAt, user.updatedAt)
    );

    console.log(`User breakdown: Featured: ${featuredUsers.length}, Verified: ${verifiedUsers.length}, New: ${newUsers.length}, Normal: ${normalUsers.length}`);

    // Combine users in priority order
    const prioritizedUsers = [
      ...featuredUsers,
      ...verifiedUsers,
      ...newUsers,
      ...normalUsers
    ];

    // Take only the first ITEMS_PER_LOAD for initial display
    const initialUsers = prioritizedUsers.slice(0, ITEMS_PER_LOAD);

    // Set up pagination for remaining users
    const remainingUsersList = prioritizedUsers.slice(ITEMS_PER_LOAD);
    const hasMoreUsers = remainingUsersList.length > 0;

    console.log(`Initial users: ${initialUsers.length}, Remaining users: ${remainingUsersList.length}, Has more: ${hasMoreUsers}`);

    setDisplayedUsers(initialUsers);
    setRemainingUsers(remainingUsersList);
    setHasMore(hasMoreUsers);
    setCurrentPage(0);

    if (fromCache) {
      console.log('Portfolios: Users processed from cache');
    } else {
      console.log('Portfolios: Users processed from fresh Firebase data');
    }
  }, [allUsers, ITEMS_PER_LOAD, fromCache]);

  // Function to load more users
  const loadMoreUsers = useCallback(async () => {
    if (!hasMore || isLoadingMore) return;

    console.log('LoadMoreUsers called:', { hasMore, isLoadingMore, remainingUsersCount: remainingUsers.length });

    try {
      setIsLoadingMore(true);

      // Get the next batch of users from remaining users
      const nextBatch = remainingUsers.slice(0, ITEMS_PER_LOAD);
      const newRemainingUsers = remainingUsers.slice(ITEMS_PER_LOAD);

      console.log('Next batch:', { nextBatchLength: nextBatch.length, newRemainingLength: newRemainingUsers.length });

      if (nextBatch.length > 0) {
        setDisplayedUsers(prev => [...prev, ...nextBatch]);
        setRemainingUsers(newRemainingUsers);
        setHasMore(newRemainingUsers.length > 0);
        setCurrentPage(prev => prev + 1);
        console.log('Users loaded successfully');
      } else {
        setHasMore(false);
        console.log('No more users to load');
      }

    } catch (err) {
      console.error('Error loading more users:', err);
    } finally {
      setIsLoadingMore(false);
    }
  }, [hasMore, isLoadingMore, remainingUsers, ITEMS_PER_LOAD]);

  // Force refresh function
  const handleRefresh = useCallback(() => {
    setDisplayedUsers([]);
    setRemainingUsers([]);
    setHasMore(true);
    setCurrentPage(0);

    // Clear cache and force refresh
    localStorage.removeItem(CACHE_KEY);
    window.location.reload(); // Simple refresh since we're using cached hooks
  }, []);

  // Generate and apply SEO metadata
  const seoMetadata = useMemo(() => {
    if (allUsers && allUsers.length > 0) {
      return generatePortfoliosSEO(allUsers);
    }
    return null;
  }, [allUsers, generatePortfoliosSEO]);

  // Apply SEO metadata
  useSEOFromMetadata(seoMetadata);

  // Process users when data is available
  useEffect(() => {
    if (allUsers && allUsers.length > 0) {
      processUsers();
    }
  }, [allUsers, processUsers]);

  const renderSeniorityIcon = useCallback(
    (seniorityLevel: string | undefined) => {
      if (!seniorityLevel || !isSeniorityLevel(seniorityLevel)) return null;

      const { icon: Icon, color } = seniorityIcons[seniorityLevel];
      return (
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="ml-1.5 p-1 rounded-full hover:bg-secondary/50 transition-colors cursor-help">
              <Icon className={`h-5 w-5 ${color}`} />
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <p className="font-medium">{seniorityLevel} Developer</p>
          </TooltipContent>
        </Tooltip>
      );
    },
    []
  );

  const renderTechStacks = useCallback((user: User) => {
    // Convert techStack map to array if it's not already an array
    const techStackArray = Array.isArray(user.techStack) 
      ? user.techStack 
      : user.techStack 
        ? Object.values(user.techStack).filter(tech => typeof tech === 'string')
        : [];
    
    const shortTechStacks = techStackArray.filter((tech) => tech.length <= 8);
    const longTechStacks = techStackArray.filter((tech) => tech.length > 8);

    return (
      <>
        <div className="flex flex-wrap justify-center gap-2">
          {shortTechStacks.slice(0, 4).map((skill, i) => (
            <span
              key={`${user.uid}-tech-${i}`}
              className="bg-muted text-foreground px-2 py-1 rounded text-xs font-medium"
            >
              {skill}
            </span>
          ))}
        </div>
        <div className="flex flex-wrap justify-center gap-2">
          {shortTechStacks.slice(4, 5).map((skill, i) => (
            <span
              key={`${user.uid}-tech-${i + 4}`}
              className="bg-muted text-foreground px-2 py-1 rounded text-xs font-medium"
            >
              {skill}
            </span>
          ))}
          {(longTechStacks.length > 0 || shortTechStacks.length > 5) && (
            <span className="bg-orange-100 text-orange-700 px-2 py-1 rounded text-xs font-medium">
              +{longTechStacks.length + Math.max(0, shortTechStacks.length - 5)}{" "}
              more
            </span>
          )}
        </div>
      </>
    );
  }, []);

  if (isLoading && displayedUsers.length === 0) {
    return (
      <TooltipProvider>
        <div className="min-h-screen bg-background">
          <section className="container mx-auto px-4 py-8">
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
              <div className="lg:col-span-4 space-y-8">
                <div className="flex flex-wrap justify-center items-start gap-8 py-8">
                  {[...Array(6)].map((_, index) => (
                    <MagicCard
                      key={index}
                      className="max-w-xs w-full flex flex-col items-center overflow-visible p-0 h-auto transition-all rounded-2xl border border-border"
                    >
                      <div className="relative w-full">
                        <Skeleton className="w-full h-32 rounded-t-2xl" />
                        <div className="absolute left-1/2 top-full -translate-x-1/2 -translate-y-1/2">
                          <Skeleton className="w-24 h-24 rounded-full border-4 border-white shadow" />
                        </div>
                      </div>
                      <div className="mt-12 w-full flex flex-col items-center px-6 flex-1">
                        <div className="flex items-center gap-1 mb-1">
                          <Skeleton className="h-6 w-32" />
                          <Skeleton className="h-4 w-4 rounded-full" />
                        </div>
                        <div className="flex items-center gap-2 mb-2">
                          <Skeleton className="h-4 w-20" />
                          <Skeleton className="h-5 w-5 rounded-full" />
                        </div>
                        <Skeleton className="h-12 w-full mb-4" />
                        <div className="flex flex-col gap-2 mb-4 w-full">
                          <div className="flex flex-wrap justify-center gap-2">
                            <Skeleton className="h-6 w-16" />
                            <Skeleton className="h-6 w-16" />
                            <Skeleton className="h-6 w-16" />
                            <Skeleton className="h-6 w-16" />
                          </div>
                          <div className="flex flex-wrap justify-center gap-2">
                            <Skeleton className="h-6 w-16" />
                            <Skeleton className="h-6 w-20" />
                          </div>
                        </div>
                        <Skeleton className="h-10 w-[200px] mt-auto mb-4" />
                      </div>
                    </MagicCard>
                  ))}
                </div>
              </div>
            </div>
          </section>
        </div>
      </TooltipProvider>
    );
  }

  if (error) {
    return (
      <TooltipProvider>
        <div className="text-center py-12">
          <div className="mx-auto w-12 h-12 rounded-full bg-destructive/10 flex items-center justify-center mb-4">
            <AlertCircle className="h-6 w-6 text-destructive" />
          </div>
          <h3 className="text-xl font-semibold mb-2">
            Failed to load profiles
          </h3>
          <p className="text-muted-foreground max-w-md mb-6">
            {typeof error === 'string' ? error : "Failed to load profiles. Please try again later."}
          </p>
          <div className="flex gap-2 justify-center">
            <Button onClick={() => window.location.reload()}>Try Again</Button>
            <Button variant="outline" onClick={handleRefresh}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh Cache
            </Button>
          </div>
        </div>
      </TooltipProvider>
    );
  }

  return (
    <TooltipProvider>
      <div className="min-h-screen bg-background">
        <section className="container mx-auto px-4 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <div className="lg:col-span-4 space-y-8">
              {!isLoading && !error && displayedUsers.length === 0 && (
                <div className="text-center py-12">
                  <div className="mx-auto w-12 h-12 rounded-full bg-muted flex items-center justify-center mb-4">
                    <Users className="h-6 w-6 text-muted-foreground" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">
                    No Profiles Found
                  </h3>
                  <p className="text-muted-foreground max-w-md mb-6">
                    There are no user profiles available at the moment.
                  </p>
                </div>
              )}

              {!isLoading && !error && displayedUsers.length > 0 && (
                <>
                  <div className="flex flex-wrap justify-center items-start gap-8 py-8">
                    {displayedUsers.map((user) => (
                      <MagicCard
                        key={`user-${user.uid}`}
                        className={`max-w-xs w-full flex flex-col items-center overflow-visible p-0 h-auto transition-all rounded-2xl ${
                          user.isFeatured
                            ? "border-2 border-[#F65100] hover:scale-[1.02] transform transition-all duration-300 relative"
                            : "border border-border hover:border-primary relative"
                        }`}
                      >
                        <div className="relative w-full">
                          {/* New Developer badge at top-right if present */}
                          {isNewProfile(user.createdAt, user.updatedAt) && (
                            <div className="absolute top-2 right-2 z-30">
                              <span
                                className="flex items-center gap-2 px-4 py-2 rounded-full font-bold text-xs text-white backdrop-blur-md"
                                style={{
                                  background: '#06c952',
                                  border: '2px solid #fff',
                                }}
                              >
                                <Star className="h-4 w-4 text-white" />
                                <span className="tracking-wide" style={{textShadow: '0 1px 8px #e52e71, 0 0 2px #fff'}}>New Developer</span>
                              </span>
                            </div>
                          )}
                          {/* Available badge: bottom-right if New Developer, else top-right */}
                          {user.availableForWork && (
                            isNewProfile(user.createdAt, user.updatedAt) ? (
                              <div className="absolute bottom-2 right-2 z-20">
                                <span className="bg-green-500 text-white px-3 py-2 rounded-full text-xs font-semibold shadow flex items-center gap-1 border-2 border-white">
                                  <CheckCircle className="h-4 w-4 text-white" />
                                  Available
                                </span>
                              </div>
                            ) : (
                              <div className="absolute top-2 right-2 z-20">
                                <span className="bg-green-500 text-white px-3 py-2 rounded-full text-xs font-semibold shadow flex items-center gap-1 border-2 border-white">
                                  <CheckCircle className="h-4 w-4 text-white" />
                                  Available
                                </span>
                              </div>
                            )
                          )}
                          {/* Featured badge remains at top-left, but offset if needed */}
                          {user.isFeatured && (
                            <div
                              className="absolute top-2 left-2 z-10"
                              style={{
                                marginTop: isNewProfile(user.createdAt, user.updatedAt) ? '2.5rem' : undefined,
                              }}
                            >
                              <span
                                className="flex items-center gap-2 px-4 py-2 rounded-full font-bold text-xs text-white backdrop-blur-md"
                                style={{
                                  background: '#F65100',
                                  border: '2px solid #fff',
                                }}
                              >
                                <Star className="h-4 w-4 text-white" />
                                <span
                                  className="tracking-wide"
                                  style={{ textShadow: '0 1px 8px #e52e71, 0 0 2px #fff' }}
                                >
                                  Featured Developer
                                </span>
                              </span>
                            </div>
                          )}
                          <img
                            src={user.bannerURL || "/placeholder-cover.jpg"}
                            alt="Banner"
                            className="w-full h-32 object-cover rounded-t-2xl"
                          />
                          <div className="absolute left-1/2 top-full -translate-x-1/2 -translate-y-1/2">
                            <div className="relative w-24 h-24">
                              <img
                                src={user.photoURL || "/placeholder-avatar.jpg"}
                                alt={user.displayName || user.username}
                                className={`w-24 h-24 rounded-full object-cover border-4 ${
                                  user.isFeatured
                                    ? "border-orange-500"
                                    : "border-white"
                                } shadow bg-transparent`}
                              />
                              {user.isFeatured && (
                                <span className="absolute bottom-0 right-0 bg-orange-500 rounded-full p-1 shadow-lg">
                                  <Crown className="h-4 w-4 text-white" />
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="mt-12 w-full flex flex-col items-center px-6 flex-1">
                          <h3 className="text-xl font-bold text-center mb-1 w-full flex items-center justify-center gap-1 text-foreground">
                            <span>{user.displayName || user.username}</span>
                            {user.isVerified && (
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="16"
                                height="16"
                                className="ml-1 inline-block align-super"
                                viewBox="0 0 48 48"
                              >
                                <polygon
                                  fill="#42a5f5"
                                  points="29.62,3 33.053,8.308 39.367,8.624 39.686,14.937 44.997,18.367 42.116,23.995 45,29.62 39.692,33.053 39.376,39.367 33.063,39.686 29.633,44.997 24.005,42.116 18.38,45 14.947,39.692 8.633,39.376 8.314,33.063 3.003,29.633 5.884,24.005 3,18.38 8.308,14.947 8.624,8.633 14.937,8.314 18.367,3.003 23.995,5.884"
                                />
                                <polygon
                                  fill="#fff"
                                  points="21.396,31.255 14.899,24.76 17.021,22.639 21.428,27.046 30.996,17.772 33.084,19.926"
                                />
                              </svg>
                            )}
                            {user.country && getCountryInfo(user.country) && (
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <div className="ml-1">
                                    <img
                                      src={getCountryInfo(user.country)?.flag}
                                      alt={getCountryInfo(user.country)?.name}
                                      className="inline-block w-6 h-6 align-middle"
                                      style={{ width: 24, height: 24, objectFit: 'contain', padding: 0, display: 'block' }}
                                    />
                                  </div>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p className="font-medium">
                                    {getCountryInfo(user.country)?.name}
                                  </p>
                                </TooltipContent>
                              </Tooltip>
                            )}
                          </h3>
                          <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground mb-2">
                            <span>{user.role || "Developer"}</span>
                            {renderSeniorityIcon(user.seniorityLevel)}
                          </div>
                          <p className="text-center text-foreground mb-4 line-clamp-2">
                            {user.bio ||
                              "A passionate developer building amazing applications."}
                          </p>
                          <div className="flex flex-col gap-2 mb-4">
                            {renderTechStacks(user)}
                          </div>
                          <div className="flex items-center justify-center gap-4 mb-4">
                            {user.linkedinUrl && (
                              <a
                                href={user.linkedinUrl}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-muted-foreground hover:text-foreground transition-colors flex items-center gap-1.5"
                              >
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="16"
                                  height="16"
                                  viewBox="0 0 24 24"
                                  fill="currentColor"
                                >
                                  <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" />
                                </svg>
                                LinkedIn
                              </a>
                            )}
                            {user.githubUrl && (
                              <a
                                href={user.githubUrl}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-muted-foreground hover:text-foreground transition-colors flex items-center gap-1.5"
                              >
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="16"
                                  height="16"
                                  viewBox="0 0 24 24"
                                  fill="currentColor"
                                >
                                  <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z" />
                                </svg>
                                GitHub
                              </a>
                            )}
                          </div>
                        </div>
                        <button
                          className={`${
                            user.isFeatured
                              ? "bg-orange-500 hover:bg-orange-600 shadow-lg hover:shadow-orange-500/25"
                              : "bg-background text-foreground border border-border hover:bg-accent hover:text-accent-foreground"
                          } py-2.5 rounded-lg font-semibold transition-all duration-300 mt-auto mb-4 mx-auto px-8 w-70 shadow-lg flex items-center justify-center gap-2 hover:scale-105`}
                          onClick={() => navigate(`/${user.username}`)}
                        >
                          <span>
                            {user.isFeatured
                              ? "View Premium Profile"
                              : "View Profile"}
                          </span>
                          <ExternalLink className="h-4 w-4" />
                        </button>
                      </MagicCard>
                    ))}
                  </div>

                  {/* Load More Button */}
                  {hasMore && (
                    <div className="flex justify-center mt-8">
                      <Button
                        onClick={loadMoreUsers}
                        disabled={isLoadingMore}
                        className="px-8 py-3 text-lg font-semibold"
                      >
                        {isLoadingMore ? (
                          <>
                            <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                            Loading More...
                          </>
                        ) : (
                          <>
                            <Users className="h-5 w-5 mr-2" />
                            Load More Developers ({users.length} loaded)
                          </>
                        )}
                      </Button>
                    </div>
                  )}

                  {/* Debug information */}
                  <div className="text-center mt-4 text-sm text-muted-foreground">
                    <p>Page: {currentPage} | Users loaded: {users.length} | Remaining: {remainingUsers.length} | Has more: {hasMore ? 'Yes' : 'No'}</p>
                    {lastDoc && <p>Last document ID: {lastDoc.id}</p>}
                  </div>

                  {/* End of results message */}
                  {!hasMore && users.length > 0 && (
                    <div className="text-center mt-8 py-8">
                      <div className="inline-flex items-center gap-2 text-muted-foreground">
                        <CheckCircle className="h-5 w-5" />
                        <span className="text-lg">You've reached the end! All developers loaded ({users.length} total).</span>
                      </div>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </section>
      </div>
    </TooltipProvider>
  );
} 