import { useState, useEffect, useCallback } from 'react';
import { getUserByUsername, type UserProfile } from '@/services/firebase-db';
import { collection, getDocs } from 'firebase/firestore';
import { db } from '@/config/firebase';

interface CacheData<T = any> {
  data: T;
  timestamp: number;
  error: string | null;
}

interface UseUserByUsernameReturn {
  data: UserProfile | null;
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

interface UseAllUsersReturn {
  data: UserProfile[];
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

interface UseInvalidateCacheReturn {
  mutateAsync: () => Promise<void>;
}

// Enhanced in-memory cache with better type safety
const cache = new Map<string, CacheData>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
const USERS_CACHE_DURATION = 10 * 60 * 1000; // 10 minutes for all users (less frequent updates)
const MAX_CACHE_SIZE = 100; // Maximum number of cache entries to prevent memory leaks

// Cache cleanup function to prevent memory leaks
const cleanupCache = () => {
  if (cache.size <= MAX_CACHE_SIZE) return;

  const entries = Array.from(cache.entries());

  // Sort by timestamp (oldest first)
  entries.sort((a, b) => a[1].timestamp - b[1].timestamp);

  // Remove oldest entries until we're under the limit
  const entriesToRemove = entries.slice(0, cache.size - MAX_CACHE_SIZE);
  entriesToRemove.forEach(([key]) => cache.delete(key));

  console.log(`Cache cleanup: removed ${entriesToRemove.length} entries`);
};

export const useUserByUsername = (username: string | undefined): UseUserByUsernameReturn => {
  const [data, setData] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchUser = useCallback(async () => {
    if (!username) {
      setData(null);
      setError(null);
      return;
    }

    const cacheKey = `user-${username}`;
    const cached = cache.get(cacheKey);
    const now = Date.now();

    // Check if we have valid cached data
    if (cached && (now - cached.timestamp) < CACHE_DURATION) {
      setData(cached.data);
      setError(cached.error);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const userData = await getUserByUsername(username);
      
      // Update cache
      cache.set(cacheKey, {
        data: userData,
        timestamp: now,
        error: null
      });

      // Cleanup cache if needed
      cleanupCache();

      setData(userData);
      setError(null);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch user data';
      
      // Update cache with error
      cache.set(cacheKey, {
        data: null,
        timestamp: now,
        error: errorMessage
      });

      setData(null);
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [username]);

  const refetch = useCallback(async () => {
    if (username) {
      // Clear cache for this user to force refetch
      cache.delete(`user-${username}`);
      await fetchUser();
    }
  }, [username, fetchUser]);

  useEffect(() => {
    fetchUser();
  }, [fetchUser]);

  return {
    data,
    isLoading,
    error,
    refetch
  };
};

// Hook to get all users with caching
export const useAllUsers = (): UseAllUsersReturn => {
  const [data, setData] = useState<UserProfile[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchAllUsers = useCallback(async () => {
    const cacheKey = 'all-users';
    const cached = cache.get(cacheKey);
    const now = Date.now();

    // Check if we have valid cached data
    if (cached && (now - cached.timestamp) < USERS_CACHE_DURATION) {
      setData(cached.data || []);
      setError(cached.error);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Fetch all users from Firestore
      const usersRef = collection(db, 'users');
      const snapshot = await getDocs(usersRef);

      const users: UserProfile[] = snapshot.docs.map(doc => ({
        uid: doc.id,
        ...doc.data()
      } as UserProfile));

      // Update cache
      cache.set(cacheKey, {
        data: users,
        timestamp: now,
        error: null
      });

      // Cleanup cache if needed
      cleanupCache();

      setData(users);
      setError(null);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch users';

      // Update cache with error
      cache.set(cacheKey, {
        data: [],
        timestamp: now,
        error: errorMessage
      });

      setData([]);
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refetch = useCallback(async () => {
    // Clear cache for all users to force refetch
    cache.delete('all-users');
    await fetchAllUsers();
  }, [fetchAllUsers]);

  useEffect(() => {
    fetchAllUsers();
  }, [fetchAllUsers]);

  return {
    data,
    isLoading,
    error,
    refetch
  };
};

// Hook to invalidate cache
export const useInvalidateUserCache = (): UseInvalidateCacheReturn => {
  const mutateAsync = useCallback(async () => {
    // Clear all user-related cache entries
    const keysToDelete: string[] = [];

    for (const [key] of cache.entries()) {
      if (key.startsWith('user-') || key === 'all-users') {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => cache.delete(key));

    console.log('Cache invalidated for user data');
  }, []);

  return {
    mutateAsync
  };
};

// Utility function to clear cache
export const clearUserCache = (username?: string) => {
  if (username) {
    cache.delete(`user-${username}`);
  } else {
    cache.clear();
  }
};

// Utility function to clear specific cache entries
export const clearCacheByPattern = (pattern: string) => {
  const keysToDelete: string[] = [];

  for (const [key] of cache.entries()) {
    if (key.includes(pattern)) {
      keysToDelete.push(key);
    }
  }

  keysToDelete.forEach(key => cache.delete(key));
};

// Get cache statistics for debugging
export const getCacheStats = () => {
  const now = Date.now();
  const stats = {
    totalEntries: cache.size,
    maxSize: MAX_CACHE_SIZE,
    entries: [] as Array<{
      key: string;
      timestamp: number;
      age: number;
      hasData: boolean;
      hasError: boolean;
      isExpired: boolean;
    }>
  };

  for (const [key, value] of cache.entries()) {
    const age = now - value.timestamp;
    const isExpired = age > (key === 'all-users' ? USERS_CACHE_DURATION : CACHE_DURATION);

    stats.entries.push({
      key,
      timestamp: value.timestamp,
      age,
      hasData: !!value.data,
      hasError: !!value.error,
      isExpired
    });
  }

  return stats;
};

// Performance monitoring hook
export const useCachePerformance = () => {
  const [stats, setStats] = useState(getCacheStats());

  const refreshStats = useCallback(() => {
    setStats(getCacheStats());
  }, []);

  useEffect(() => {
    const interval = setInterval(refreshStats, 10000); // Update every 10 seconds
    return () => clearInterval(interval);
  }, [refreshStats]);

  return {
    stats,
    refreshStats,
    clearCache: () => {
      cache.clear();
      refreshStats();
    }
  };
};