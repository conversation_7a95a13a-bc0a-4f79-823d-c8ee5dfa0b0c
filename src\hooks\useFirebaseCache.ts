import { useState, useEffect, useCallback } from 'react';
import { getUserByUsername, type UserProfile } from '@/services/firebase-db';
import { collection, getDocs } from 'firebase/firestore';
import { db } from '@/config/firebase';
import { cacheAnalytics } from '@/services/cache-analytics';

interface CacheData<T = any> {
  data: T;
  timestamp: number;
  error: string | null;
  hitCount?: number;
  lastAccessed?: number;
}

interface UseUserByUsernameReturn {
  data: UserProfile | null;
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  fromCache?: boolean;
}

interface UseAllUsersReturn {
  data: UserProfile[];
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  fromCache?: boolean;
}

interface UseInvalidateCacheReturn {
  mutateAsync: () => Promise<void>;
}

interface CacheStats {
  totalEntries: number;
  maxSize: number;
  hitRate: number;
  totalHits: number;
  totalMisses: number;
  entries: Array<{
    key: string;
    timestamp: number;
    age: number;
    hasData: boolean;
    hasError: boolean;
    isExpired: boolean;
    hitCount: number;
    lastAccessed: number;
  }>;
}

// Enhanced cache configuration
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
const USERS_CACHE_DURATION = 10 * 60 * 1000; // 10 minutes for all users (less frequent updates)
const MAX_CACHE_SIZE = 100; // Maximum number of cache entries to prevent memory leaks
const STORAGE_KEY_PREFIX = 'firebase_cache_';
const CACHE_VERSION = '1.0';

// Cache statistics
let cacheStats = {
  hits: 0,
  misses: 0,
  totalRequests: 0
};

// Enhanced persistent cache with localStorage backup
class PersistentCache {
  private memoryCache = new Map<string, CacheData>();

  constructor() {
    this.loadFromStorage();
  }

  private getStorageKey(key: string): string {
    return `${STORAGE_KEY_PREFIX}${key}`;
  }

  private loadFromStorage(): void {
    try {
      // Load cache entries from localStorage
      const keys = Object.keys(localStorage).filter(key =>
        key.startsWith(STORAGE_KEY_PREFIX)
      );

      for (const storageKey of keys) {
        try {
          const cacheKey = storageKey.replace(STORAGE_KEY_PREFIX, '');
          const stored = localStorage.getItem(storageKey);

          if (stored) {
            const parsed = JSON.parse(stored);

            // Validate cache version and expiration
            if (parsed.version === CACHE_VERSION && this.isValidCacheEntry(parsed)) {
              this.memoryCache.set(cacheKey, {
                data: parsed.data,
                timestamp: parsed.timestamp,
                error: parsed.error,
                hitCount: parsed.hitCount || 0,
                lastAccessed: parsed.lastAccessed || parsed.timestamp
              });
            } else {
              // Remove invalid/expired entries
              localStorage.removeItem(storageKey);
            }
          }
        } catch (error) {
          console.warn(`Failed to load cache entry ${storageKey}:`, error);
          localStorage.removeItem(storageKey);
        }
      }

      console.log(`Loaded ${this.memoryCache.size} cache entries from storage`);
    } catch (error) {
      console.warn('Failed to load cache from storage:', error);
    }
  }

  private isValidCacheEntry(entry: any): boolean {
    if (!entry || typeof entry.timestamp !== 'number') return false;

    const now = Date.now();
    const age = now - entry.timestamp;
    const maxAge = entry.key === 'all-users' ? USERS_CACHE_DURATION : CACHE_DURATION;

    return age < maxAge;
  }

  private saveToStorage(key: string, data: CacheData): void {
    try {
      const storageKey = this.getStorageKey(key);
      const toStore = {
        version: CACHE_VERSION,
        key,
        data: data.data,
        timestamp: data.timestamp,
        error: data.error,
        hitCount: data.hitCount || 0,
        lastAccessed: data.lastAccessed || data.timestamp
      };

      localStorage.setItem(storageKey, JSON.stringify(toStore));
    } catch (error) {
      console.warn(`Failed to save cache entry ${key} to storage:`, error);
      // If localStorage is full, try to clean up old entries
      this.cleanupStorage();
    }
  }

  private cleanupStorage(): void {
    try {
      const keys = Object.keys(localStorage).filter(key =>
        key.startsWith(STORAGE_KEY_PREFIX)
      );

      // Remove expired entries first
      for (const storageKey of keys) {
        try {
          const stored = localStorage.getItem(storageKey);
          if (stored) {
            const parsed = JSON.parse(stored);
            if (!this.isValidCacheEntry(parsed)) {
              localStorage.removeItem(storageKey);
            }
          }
        } catch {
          localStorage.removeItem(storageKey);
        }
      }
    } catch (error) {
      console.warn('Failed to cleanup storage:', error);
    }
  }

  get(key: string): CacheData | undefined {
    const startTime = Date.now();
    const cached = this.memoryCache.get(key);

    if (cached) {
      // Update access statistics
      cached.hitCount = (cached.hitCount || 0) + 1;
      cached.lastAccessed = Date.now();
      cacheStats.hits++;
      cacheStats.totalRequests++;

      // Track analytics
      cacheAnalytics.trackEvent({
        type: 'hit',
        key,
        duration: Date.now() - startTime,
        source: 'memory',
        size: JSON.stringify(cached.data).length
      });

      // Update storage with new stats
      this.saveToStorage(key, cached);

      return cached;
    }

    cacheStats.misses++;
    cacheStats.totalRequests++;

    // Track analytics
    cacheAnalytics.trackEvent({
      type: 'miss',
      key,
      duration: Date.now() - startTime,
      source: 'memory'
    });

    return undefined;
  }

  set(key: string, data: CacheData): void {
    const startTime = Date.now();

    // Add access tracking
    const enhancedData = {
      ...data,
      hitCount: 0,
      lastAccessed: Date.now()
    };

    this.memoryCache.set(key, enhancedData);
    this.saveToStorage(key, enhancedData);
    this.cleanup();

    // Track analytics
    cacheAnalytics.trackEvent({
      type: 'set',
      key,
      duration: Date.now() - startTime,
      source: 'memory',
      size: JSON.stringify(data.data).length
    });
  }

  delete(key: string): boolean {
    const deleted = this.memoryCache.delete(key);

    try {
      localStorage.removeItem(this.getStorageKey(key));
    } catch (error) {
      console.warn(`Failed to remove ${key} from storage:`, error);
    }

    // Track analytics
    if (deleted) {
      cacheAnalytics.trackEvent({
        type: 'delete',
        key,
        source: 'memory'
      });
    }

    return deleted;
  }

  clear(): void {
    const clearedKeys = Array.from(this.memoryCache.keys());
    this.memoryCache.clear();

    try {
      const keys = Object.keys(localStorage).filter(key =>
        key.startsWith(STORAGE_KEY_PREFIX)
      );
      keys.forEach(key => localStorage.removeItem(key));
    } catch (error) {
      console.warn('Failed to clear storage cache:', error);
    }

    // Track analytics
    cacheAnalytics.trackEvent({
      type: 'clear',
      key: 'all',
      source: 'memory',
      metadata: { clearedCount: clearedKeys.length }
    });
  }

  get size(): number {
    return this.memoryCache.size;
  }

  entries(): IterableIterator<[string, CacheData]> {
    return this.memoryCache.entries();
  }

  private cleanup(): void {
    if (this.memoryCache.size <= MAX_CACHE_SIZE) return;

    const entries = Array.from(this.memoryCache.entries());

    // Sort by last accessed time and hit count (LRU with popularity)
    entries.sort((a, b) => {
      const aScore = (a[1].lastAccessed || 0) + (a[1].hitCount || 0) * 1000;
      const bScore = (b[1].lastAccessed || 0) + (b[1].hitCount || 0) * 1000;
      return aScore - bScore;
    });

    // Remove least recently used entries
    const entriesToRemove = entries.slice(0, this.memoryCache.size - MAX_CACHE_SIZE);
    entriesToRemove.forEach(([key]) => {
      this.delete(key);
    });

    console.log(`Cache cleanup: removed ${entriesToRemove.length} entries`);
  }
}

// Initialize the enhanced cache
const cache = new PersistentCache();

export const useUserByUsername = (username: string | undefined): UseUserByUsernameReturn => {
  const [data, setData] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [fromCache, setFromCache] = useState(false);

  const fetchUser = useCallback(async () => {
    if (!username) {
      setData(null);
      setError(null);
      setFromCache(false);
      return;
    }

    const cacheKey = `user-${username}`;
    const cached = cache.get(cacheKey);
    const now = Date.now();

    // Check if we have valid cached data
    if (cached && (now - cached.timestamp) < CACHE_DURATION) {
      setData(cached.data);
      setError(cached.error);
      setFromCache(true);
      console.log(`Cache hit for user: ${username}`);
      return;
    }

    setIsLoading(true);
    setError(null);
    setFromCache(false);

    try {
      console.log(`Cache miss for user: ${username}, fetching from Firebase...`);
      const fetchStart = Date.now();
      const userData = await getUserByUsername(username);
      const fetchDuration = Date.now() - fetchStart;

      // Track Firebase fetch analytics
      cacheAnalytics.trackEvent({
        type: 'miss',
        key: cacheKey,
        duration: fetchDuration,
        source: 'firebase',
        size: JSON.stringify(userData).length
      });

      // Update cache
      cache.set(cacheKey, {
        data: userData,
        timestamp: now,
        error: null
      });

      setData(userData);
      setError(null);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch user data';

      // Update cache with error
      cache.set(cacheKey, {
        data: null,
        timestamp: now,
        error: errorMessage
      });

      setData(null);
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [username]);

  const refetch = useCallback(async () => {
    if (username) {
      // Clear cache for this user to force refetch
      cache.delete(`user-${username}`);
      await fetchUser();
    }
  }, [username, fetchUser]);

  useEffect(() => {
    fetchUser();
  }, [fetchUser]);

  return {
    data,
    isLoading,
    error,
    refetch,
    fromCache
  };
};

// Hook to get all users with caching
export const useAllUsers = (): UseAllUsersReturn => {
  const [data, setData] = useState<UserProfile[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [fromCache, setFromCache] = useState(false);

  const fetchAllUsers = useCallback(async () => {
    const cacheKey = 'all-users';
    const cached = cache.get(cacheKey);
    const now = Date.now();

    // Check if we have valid cached data
    if (cached && (now - cached.timestamp) < USERS_CACHE_DURATION) {
      setData(cached.data || []);
      setError(cached.error);
      setFromCache(true);
      console.log('Cache hit for all users');
      return;
    }

    setIsLoading(true);
    setError(null);
    setFromCache(false);

    try {
      console.log('Cache miss for all users, fetching from Firebase...');
      const fetchStart = Date.now();

      // Fetch all users from Firestore
      const usersRef = collection(db, 'users');
      const snapshot = await getDocs(usersRef);

      const users: UserProfile[] = snapshot.docs.map(doc => ({
        uid: doc.id,
        ...doc.data()
      } as UserProfile));

      const fetchDuration = Date.now() - fetchStart;
      console.log(`Fetched ${users.length} users from Firebase in ${fetchDuration}ms`);

      // Track Firebase fetch analytics
      cacheAnalytics.trackEvent({
        type: 'miss',
        key: cacheKey,
        duration: fetchDuration,
        source: 'firebase',
        size: JSON.stringify(users).length,
        metadata: { userCount: users.length }
      });

      // Update cache
      cache.set(cacheKey, {
        data: users,
        timestamp: now,
        error: null
      });

      setData(users);
      setError(null);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch users';

      // Update cache with error
      cache.set(cacheKey, {
        data: [],
        timestamp: now,
        error: errorMessage
      });

      setData([]);
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refetch = useCallback(async () => {
    // Clear cache for all users to force refetch
    cache.delete('all-users');
    await fetchAllUsers();
  }, [fetchAllUsers]);

  useEffect(() => {
    fetchAllUsers();
  }, [fetchAllUsers]);

  return {
    data,
    isLoading,
    error,
    refetch,
    fromCache
  };
};

// Hook to invalidate cache
export const useInvalidateUserCache = (): UseInvalidateCacheReturn => {
  const mutateAsync = useCallback(async () => {
    // Clear all user-related cache entries
    const keysToDelete: string[] = [];

    for (const [key] of cache.entries()) {
      if (key.startsWith('user-') || key === 'all-users') {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => cache.delete(key));

    console.log('Cache invalidated for user data');
  }, []);

  return {
    mutateAsync
  };
};

// Enhanced cache invalidation functions
export const clearUserCache = (username?: string) => {
  if (username) {
    const key = `user-${username}`;
    cache.delete(key);
    console.log(`Cleared cache for user: ${username}`);
  } else {
    // Clear all user caches
    const keysToDelete: string[] = [];
    for (const [key] of cache.entries()) {
      if (key.startsWith('user-')) {
        keysToDelete.push(key);
      }
    }
    keysToDelete.forEach(key => cache.delete(key));
    console.log(`Cleared ${keysToDelete.length} user cache entries`);
  }
};

export const clearCacheByPattern = (pattern: string) => {
  const keysToDelete: string[] = [];
  const regex = new RegExp(pattern);

  for (const [key] of cache.entries()) {
    if (regex.test(key)) {
      keysToDelete.push(key);
    }
  }

  keysToDelete.forEach(key => cache.delete(key));
  console.log(`Cleared ${keysToDelete.length} cache entries matching pattern: ${pattern}`);
};

// Smart invalidation based on user actions
export const invalidateOnUserUpdate = (username: string) => {
  // Clear specific user cache
  clearUserCache(username);

  // Clear all users cache since it contains the updated user
  cache.delete('all-users');

  // Clear any featured/verified user caches if they exist
  cache.delete('featured-users');
  cache.delete('verified-users');

  console.log(`Smart invalidation completed for user: ${username}`);
};

export const invalidateOnNewUser = () => {
  // Clear all users cache
  cache.delete('all-users');

  // Clear new users cache if it exists
  cache.delete('new-users');

  console.log('Cache invalidated for new user registration');
};

// Time-based invalidation
export const cleanupExpiredEntries = () => {
  const now = Date.now();
  const keysToDelete: string[] = [];

  for (const [key, data] of cache.entries()) {
    const maxAge = key === 'all-users' ? USERS_CACHE_DURATION : CACHE_DURATION;
    const age = now - data.timestamp;

    if (age > maxAge) {
      keysToDelete.push(key);
    }
  }

  keysToDelete.forEach(key => cache.delete(key));

  if (keysToDelete.length > 0) {
    console.log(`Cleaned up ${keysToDelete.length} expired cache entries`);
  }

  return keysToDelete.length;
};

// Automatic cleanup interval
let cleanupInterval: NodeJS.Timeout | null = null;

export const startAutomaticCleanup = (intervalMs: number = 5 * 60 * 1000) => {
  if (cleanupInterval) {
    clearInterval(cleanupInterval);
  }

  cleanupInterval = setInterval(() => {
    cleanupExpiredEntries();
  }, intervalMs);

  console.log(`Started automatic cache cleanup every ${intervalMs / 1000}s`);
};

export const stopAutomaticCleanup = () => {
  if (cleanupInterval) {
    clearInterval(cleanupInterval);
    cleanupInterval = null;
    console.log('Stopped automatic cache cleanup');
  }
};

// Get enhanced cache statistics for debugging and monitoring
export const getCacheStats = (): CacheStats => {
  const now = Date.now();
  const hitRate = cacheStats.totalRequests > 0
    ? (cacheStats.hits / cacheStats.totalRequests) * 100
    : 0;

  const stats: CacheStats = {
    totalEntries: cache.size,
    maxSize: MAX_CACHE_SIZE,
    hitRate: Math.round(hitRate * 100) / 100,
    totalHits: cacheStats.hits,
    totalMisses: cacheStats.misses,
    entries: []
  };

  for (const [key, value] of cache.entries()) {
    const age = now - value.timestamp;
    const isExpired = age > (key === 'all-users' ? USERS_CACHE_DURATION : CACHE_DURATION);

    stats.entries.push({
      key,
      timestamp: value.timestamp,
      age,
      hasData: !!value.data,
      hasError: !!value.error,
      isExpired,
      hitCount: value.hitCount || 0,
      lastAccessed: value.lastAccessed || value.timestamp
    });
  }

  return stats;
};

// Reset cache statistics
export const resetCacheStats = () => {
  cacheStats = {
    hits: 0,
    misses: 0,
    totalRequests: 0
  };
};

// Enhanced performance monitoring hook
export const useCachePerformance = () => {
  const [stats, setStats] = useState(getCacheStats());

  const refreshStats = useCallback(() => {
    setStats(getCacheStats());
  }, []);

  useEffect(() => {
    const interval = setInterval(refreshStats, 10000); // Update every 10 seconds
    return () => clearInterval(interval);
  }, [refreshStats]);

  const clearCache = useCallback(() => {
    cache.clear();
    resetCacheStats();
    refreshStats();
  }, [refreshStats]);

  const preloadCache = useCallback(async () => {
    console.log('Preloading cache with frequently accessed data...');

    try {
      // Preload all users if not already cached
      const allUsersKey = 'all-users';
      const cached = cache.get(allUsersKey);
      const now = Date.now();

      if (!cached || (now - cached.timestamp) >= USERS_CACHE_DURATION) {
        const usersRef = collection(db, 'users');
        const snapshot = await getDocs(usersRef);
        const users: UserProfile[] = snapshot.docs.map(doc => ({
          uid: doc.id,
          ...doc.data()
        } as UserProfile));

        cache.set(allUsersKey, {
          data: users,
          timestamp: now,
          error: null
        });

        console.log(`Preloaded ${users.length} users to cache`);
      }

      refreshStats();
    } catch (error) {
      console.error('Failed to preload cache:', error);
    }
  }, [refreshStats]);

  return {
    stats,
    refreshStats,
    clearCache,
    preloadCache,
    resetStats: resetCacheStats
  };
};